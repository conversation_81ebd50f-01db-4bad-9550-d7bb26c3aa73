"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  FileText,
  MessageSquare,
  Code,
  Palette,
  Brain,
  Zap,
  Search,
  Plus,
  Download
} from "lucide-react"

interface SidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const moduleCategories = [
  {
    name: "Core Modules",
    icon: Brain,
    modules: [
      { name: "System Context", description: "Define AI behavior and role", icon: MessageSquare, tags: ["system", "context"] },
      { name: "User Input", description: "Capture user requirements", icon: FileText, tags: ["input", "user"] },
      { name: "Output Format", description: "Structure response format", icon: Code, tags: ["output", "format"] },
    ]
  },
  {
    name: "Prompt Engines",
    icon: Zap,
    modules: [
      { name: "Writer Engine", description: "Outline → Expand → Style", icon: Palette, tags: ["writing", "engine"] },
      { name: "Interview Bot", description: "Role-play Q&A trainer", icon: MessageSquare, tags: ["interview", "qa"] },
      { name: "Debug Console", description: "Analyze and fix code", icon: Code, tags: ["debug", "code"] },
    ]
  },
  {
    name: "Specialized",
    icon: Palette,
    modules: [
      { name: "Chain of Draft", description: "Multi-stage refinement", icon: FileText, tags: ["chain", "draft"] },
      { name: "Model Adapter", description: "Optimize for target model", icon: Brain, tags: ["adapter", "model"] },
      { name: "Memory Context", description: "Session-based memory", icon: Zap, tags: ["memory", "context"] },
    ]
  }
]

const savedModules = [
  { id: "1", name: "Blog Writer", icon: FileText, tags: ["writing", "blog"] },
  { id: "2", name: "Code Reviewer", icon: Code, tags: ["code", "review"] },
  { id: "3", name: "Research Assistant", icon: Brain, tags: ["research", "analysis"] },
]

export function Sidebar({ collapsed, onToggleCollapse }: SidebarProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(["Core Modules"])
  const [searchQuery, setSearchQuery] = useState("")

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryName)
        ? prev.filter(name => name !== categoryName)
        : [...prev, categoryName]
    )
  }

  return (
    <div className={`${collapsed ? 'w-16' : 'w-80'} border-r border-border bg-card/20 backdrop-blur-sm transition-all duration-300 flex flex-col`}>
      {/* Sidebar Header */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        {!collapsed && (
          <div>
            <h2 className="font-semibold text-sm">Module Library</h2>
            <p className="text-xs text-muted-foreground">Engines & Modules</p>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="p-2"
        >
          {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Search */}
          {!collapsed && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search modules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 catalyst-border"
              />
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-2">
            {!collapsed && <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Quick Start</h3>}

            <Button
              variant="outline"
              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}
            >
              <Brain className="w-4 h-4 text-catalyst-purple" />
              {!collapsed && "Prompt4Me Assistant"}
            </Button>

            <Button
              variant="outline"
              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}
            >
              <Palette className="w-4 h-4 text-catalyst-teal" />
              {!collapsed && "PromptForge Lite"}
            </Button>
          </div>

          {/* Module Categories */}
          <div className="space-y-4">
            {!collapsed && <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Module Categories</h3>}

            {moduleCategories.map((category) => {
              const isExpanded = expandedCategories.includes(category.name)
              const Icon = category.icon

              return (
                <Card key={category.name} className="catalyst-border">
                  <CardHeader
                    className="pb-2 cursor-pointer"
                    onClick={() => !collapsed && toggleCategory(category.name)}
                  >
                    <CardTitle className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Icon className="w-4 h-4 text-catalyst-purple" />
                        {!collapsed && category.name}
                      </div>
                      {!collapsed && (isExpanded ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      ))}
                    </CardTitle>
                  </CardHeader>

                  {!collapsed && isExpanded && (
                    <CardContent className="pt-0 space-y-2">
                      {category.modules.map((module) => {
                        const ModuleIcon = module.icon
                        return (
                          <Card key={module.name} className="module-card cursor-pointer p-3">
                            <div className="flex items-start gap-3">
                              <ModuleIcon className="w-4 h-4 text-catalyst-purple mt-0.5" />
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-sm mb-1">{module.name}</h4>
                                <p className="text-xs text-muted-foreground mb-2">{module.description}</p>
                                <div className="flex flex-wrap gap-1">
                                  {module.tags.map((tag) => (
                                    <Badge key={tag} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </Card>
                        )
                      })}
                    </CardContent>
                  )}
                </Card>
              )
            })}
          </div>

          {/* Saved Modules */}
          {!collapsed && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Saved Modules</h3>
                <Button variant="ghost" size="sm" className="p-1">
                  <Plus className="w-3 h-3" />
                </Button>
              </div>

              {savedModules.map((module) => (
                <Card key={module.id} className="module-card cursor-pointer p-3">
                  <div className="flex items-start gap-3">
                    <module.icon className="w-4 h-4 text-catalyst-purple mt-0.5" />
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm mb-1">{module.name}</h4>
                      <div className="flex flex-wrap gap-1">
                        {module.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
