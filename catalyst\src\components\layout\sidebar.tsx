"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { 
  FileText, 
  Zap, 
  MessageSquare, 
  Bug, 
  Brain, 
  ChevronDown,
  Plus
} from "lucide-react"

const moduleCategories = [
  {
    name: "Core Modules",
    icon: FileText,
    modules: [
      { name: "Text Generator", description: "Generate text content" },
      { name: "Code Assistant", description: "Help with coding tasks" },
      { name: "Data Analyzer", description: "Analyze and interpret data" },
    ]
  },
  {
    name: "Prompt Engines",
    icon: Zap,
    modules: [
      { name: "Writer Engine", description: "Creative writing assistant" },
      { name: "Interview Engine", description: "Conduct structured interviews" },
      { name: "Debug Engine", description: "Debug and troubleshoot" },
    ]
  },
  {
    name: "Specialized",
    icon: Brain,
    modules: [
      { name: "Chain of Draft", description: "Iterative content refinement" },
      { name: "Prompt4Me", description: "Intelligent prompt suggestions" },
    ]
  }
]

export function Sidebar() {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([
    "Core Modules"
  ])

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryName)
        ? prev.filter(name => name !== categoryName)
        : [...prev, categoryName]
    )
  }

  return (
    <aside className="w-80 border-r border-border bg-card/30 backdrop-blur-sm">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Module Library</h2>
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-2">
          {moduleCategories.map((category) => {
            const Icon = category.icon
            const isExpanded = expandedCategories.includes(category.name)

            return (
              <div key={category.name} className="space-y-1">
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => toggleCategory(category.name)}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {category.name}
                  <ChevronDown 
                    className={`h-4 w-4 ml-auto transition-transform ${
                      isExpanded ? "rotate-180" : ""
                    }`} 
                  />
                </Button>

                {isExpanded && (
                  <div className="ml-6 space-y-1">
                    {category.modules.map((module) => (
                      <div
                        key={module.name}
                        className="p-3 rounded-md border border-border/50 bg-background/50 hover:bg-accent/50 cursor-pointer transition-colors"
                        draggable
                        onDragStart={(e) => {
                          e.dataTransfer.setData("text/plain", JSON.stringify(module))
                        }}
                      >
                        <div className="text-sm font-medium">{module.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {module.description}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>
    </aside>
  )
}
