{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        catalyst: \"catalyst-gradient-bg text-white shadow-lg catalyst-glow-hover\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\nimport { Settings, Save, Download, Upload } from \"lucide-react\"\n\nexport function Header() {\n  return (\n    <header className=\"border-b border-border bg-card/50 backdrop-blur-sm\">\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Logo and Title */}\n        <div className=\"flex items-center gap-3\">\n          <Image\n            src=\"/catalyst-logo.png\"\n            alt=\"Catalyst\"\n            width={32}\n            height={32}\n            className=\"catalyst-glow\"\n          />\n          <div>\n            <h1 className=\"text-xl font-bold catalyst-gradient-text\">\n              Catalyst\n            </h1>\n            <p className=\"text-xs text-muted-foreground\">\n              Prompt Engineering Ecosystem\n            </p>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"ghost\" size=\"sm\">\n            <Upload className=\"h-4 w-4\" />\n            Import\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Download className=\"h-4 w-4\" />\n            Export\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Save className=\"h-4 w-4\" />\n            Save\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Settings className=\"h-4 w-4\" />\n            Settings\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAOjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGhC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;KA7CgB", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport {\n  ChevronDown,\n  ChevronRight,\n  ChevronLeft,\n  FileText,\n  MessageSquare,\n  Code,\n  Palette,\n  Brain,\n  Zap,\n  Search,\n  Plus,\n  Download\n} from \"lucide-react\"\n\ninterface SidebarProps {\n  collapsed: boolean;\n  onToggleCollapse: () => void;\n}\n\nconst moduleCategories = [\n  {\n    name: \"Core Modules\",\n    icon: Brain,\n    modules: [\n      { name: \"System Context\", description: \"Define AI behavior and role\", icon: MessageSquare, tags: [\"system\", \"context\"] },\n      { name: \"User Input\", description: \"Capture user requirements\", icon: FileText, tags: [\"input\", \"user\"] },\n      { name: \"Output Format\", description: \"Structure response format\", icon: Code, tags: [\"output\", \"format\"] },\n    ]\n  },\n  {\n    name: \"Prompt Engines\",\n    icon: Zap,\n    modules: [\n      { name: \"Writer Engine\", description: \"Outline → Expand → Style\", icon: Palette, tags: [\"writing\", \"engine\"] },\n      { name: \"Interview Bot\", description: \"Role-play Q&A trainer\", icon: MessageSquare, tags: [\"interview\", \"qa\"] },\n      { name: \"Debug Console\", description: \"Analyze and fix code\", icon: Code, tags: [\"debug\", \"code\"] },\n    ]\n  },\n  {\n    name: \"Specialized\",\n    icon: Palette,\n    modules: [\n      { name: \"Chain of Draft\", description: \"Multi-stage refinement\", icon: FileText, tags: [\"chain\", \"draft\"] },\n      { name: \"Model Adapter\", description: \"Optimize for target model\", icon: Brain, tags: [\"adapter\", \"model\"] },\n      { name: \"Memory Context\", description: \"Session-based memory\", icon: Zap, tags: [\"memory\", \"context\"] },\n    ]\n  }\n]\n\nconst savedModules = [\n  { id: \"1\", name: \"Blog Writer\", icon: FileText, tags: [\"writing\", \"blog\"] },\n  { id: \"2\", name: \"Code Reviewer\", icon: Code, tags: [\"code\", \"review\"] },\n  { id: \"3\", name: \"Research Assistant\", icon: Brain, tags: [\"research\", \"analysis\"] },\n]\n\nexport function Sidebar({ collapsed, onToggleCollapse }: SidebarProps) {\n  const [expandedCategories, setExpandedCategories] = useState<string[]>([\"Core Modules\"])\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  const toggleCategory = (categoryName: string) => {\n    setExpandedCategories(prev =>\n      prev.includes(categoryName)\n        ? prev.filter(name => name !== categoryName)\n        : [...prev, categoryName]\n    )\n  }\n\n  return (\n    <div className={`${collapsed ? 'w-16' : 'w-80'} border-r border-border bg-card/20 backdrop-blur-sm transition-all duration-300 flex flex-col`}>\n      {/* Sidebar Header */}\n      <div className=\"p-4 border-b border-border flex items-center justify-between\">\n        {!collapsed && (\n          <div>\n            <h2 className=\"font-semibold text-sm\">Module Library</h2>\n            <p className=\"text-xs text-muted-foreground\">Engines & Modules</p>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={onToggleCollapse}\n          className=\"p-2\"\n        >\n          {collapsed ? <ChevronRight className=\"w-4 h-4\" /> : <ChevronLeft className=\"w-4 h-4\" />}\n        </Button>\n      </div>\n\n      <ScrollArea className=\"flex-1\">\n        <div className=\"p-4 space-y-6\">\n          {/* Search */}\n          {!collapsed && (\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search modules...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 catalyst-border\"\n              />\n            </div>\n          )}\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            {!collapsed && <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Quick Start</h3>}\n\n            <Button\n              variant=\"outline\"\n              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}\n            >\n              <Brain className=\"w-4 h-4 text-catalyst-purple\" />\n              {!collapsed && \"Prompt4Me Assistant\"}\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}\n            >\n              <Palette className=\"w-4 h-4 text-catalyst-teal\" />\n              {!collapsed && \"PromptForge Lite\"}\n            </Button>\n          </div>\n\n          {/* Module Categories */}\n          <div className=\"space-y-4\">\n            {!collapsed && <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Module Categories</h3>}\n\n            {moduleCategories.map((category) => {\n              const isExpanded = expandedCategories.includes(category.name)\n              const Icon = category.icon\n\n              return (\n                <Card key={category.name} className=\"catalyst-border\">\n                  <CardHeader\n                    className=\"pb-2 cursor-pointer\"\n                    onClick={() => !collapsed && toggleCategory(category.name)}\n                  >\n                    <CardTitle className=\"flex items-center justify-between text-sm\">\n                      <div className=\"flex items-center gap-2\">\n                        <Icon className=\"w-4 h-4 text-catalyst-purple\" />\n                        {!collapsed && category.name}\n                      </div>\n                      {!collapsed && (isExpanded ? (\n                        <ChevronDown className=\"w-4 h-4\" />\n                      ) : (\n                        <ChevronRight className=\"w-4 h-4\" />\n                      ))}\n                    </CardTitle>\n                  </CardHeader>\n\n                  {!collapsed && isExpanded && (\n                    <CardContent className=\"pt-0 space-y-2\">\n                      {category.modules.map((module) => {\n                        const ModuleIcon = module.icon\n                        return (\n                          <Card key={module.name} className=\"module-card cursor-pointer p-3\">\n                            <div className=\"flex items-start gap-3\">\n                              <ModuleIcon className=\"w-4 h-4 text-catalyst-purple mt-0.5\" />\n                              <div className=\"flex-1 min-w-0\">\n                                <h4 className=\"font-medium text-sm mb-1\">{module.name}</h4>\n                                <p className=\"text-xs text-muted-foreground mb-2\">{module.description}</p>\n                                <div className=\"flex flex-wrap gap-1\">\n                                  {module.tags.map((tag) => (\n                                    <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                                      {tag}\n                                    </Badge>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        )\n                      })}\n                    </CardContent>\n                  )}\n                </Card>\n              )\n            })}\n          </div>\n\n          {/* Saved Modules */}\n          {!collapsed && (\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Saved Modules</h3>\n                <Button variant=\"ghost\" size=\"sm\" className=\"p-1\">\n                  <Plus className=\"w-3 h-3\" />\n                </Button>\n              </div>\n\n              {savedModules.map((module) => (\n                <Card key={module.id} className=\"module-card cursor-pointer p-3\">\n                  <div className=\"flex items-start gap-3\">\n                    <module.icon className=\"w-4 h-4 text-catalyst-purple mt-0.5\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-sm mb-1\">{module.name}</h4>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {module.tags.map((tag) => (\n                          <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA4BA,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;gBAA+B,MAAM,2NAAA,CAAA,gBAAa;gBAAE,MAAM;oBAAC;oBAAU;iBAAU;YAAC;YACvH;gBAAE,MAAM;gBAAc,aAAa;gBAA6B,MAAM,iNAAA,CAAA,WAAQ;gBAAE,MAAM;oBAAC;oBAAS;iBAAO;YAAC;YACxG;gBAAE,MAAM;gBAAiB,aAAa;gBAA6B,MAAM,qMAAA,CAAA,OAAI;gBAAE,MAAM;oBAAC;oBAAU;iBAAS;YAAC;SAC3G;IACH;IACA;QACE,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,SAAS;YACP;gBAAE,MAAM;gBAAiB,aAAa;gBAA4B,MAAM,2MAAA,CAAA,UAAO;gBAAE,MAAM;oBAAC;oBAAW;iBAAS;YAAC;YAC7G;gBAAE,MAAM;gBAAiB,aAAa;gBAAyB,MAAM,2NAAA,CAAA,gBAAa;gBAAE,MAAM;oBAAC;oBAAa;iBAAK;YAAC;YAC9G;gBAAE,MAAM;gBAAiB,aAAa;gBAAwB,MAAM,qMAAA,CAAA,OAAI;gBAAE,MAAM;oBAAC;oBAAS;iBAAO;YAAC;SACnG;IACH;IACA;QACE,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;gBAA0B,MAAM,iNAAA,CAAA,WAAQ;gBAAE,MAAM;oBAAC;oBAAS;iBAAQ;YAAC;YAC1G;gBAAE,MAAM;gBAAiB,aAAa;gBAA6B,MAAM,uMAAA,CAAA,QAAK;gBAAE,MAAM;oBAAC;oBAAW;iBAAQ;YAAC;YAC3G;gBAAE,MAAM;gBAAkB,aAAa;gBAAwB,MAAM,mMAAA,CAAA,MAAG;gBAAE,MAAM;oBAAC;oBAAU;iBAAU;YAAC;SACvG;IACH;CACD;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM,iNAAA,CAAA,WAAQ;QAAE,MAAM;YAAC;YAAW;SAAO;IAAC;IAC1E;QAAE,IAAI;QAAK,MAAM;QAAiB,MAAM,qMAAA,CAAA,OAAI;QAAE,MAAM;YAAC;YAAQ;SAAS;IAAC;IACvE;QAAE,IAAI;QAAK,MAAM;QAAsB,MAAM,uMAAA,CAAA,QAAK;QAAE,MAAM;YAAC;YAAY;SAAW;IAAC;CACpF;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAgB;;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAe;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,gBACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,gBAC7B;mBAAI;gBAAM;aAAa;IAE/B;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,YAAY,SAAS,OAAO,6FAA6F,CAAC;;0BAE3I,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAGjD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAET,0BAAY,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAAe,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI/E,6LAAC;gBAAW,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,CAAC,2BACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,2BAAa,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAEjG,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,GAAG,YAAY,eAAe,6BAA6B,oCAAoC,CAAC;;sDAE3G,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,CAAC,aAAa;;;;;;;8CAGjB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,GAAG,YAAY,eAAe,6BAA6B,oCAAoC,CAAC;;sDAE3G,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,CAAC,aAAa;;;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,2BAAa,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;gCAEhG,iBAAiB,GAAG,CAAC,CAAC;oCACrB,MAAM,aAAa,mBAAmB,QAAQ,CAAC,SAAS,IAAI;oCAC5D,MAAM,OAAO,SAAS,IAAI;oCAE1B,qBACE,6LAAC;wCAAyB,WAAU;;0DAClC,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,CAAC,aAAa,eAAe,SAAS,IAAI;0DAEzD,cAAA,6LAAC;oDAAU,WAAU;;sEACnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;;;;;gEACf,CAAC,aAAa,SAAS,IAAI;;;;;;;wDAE7B,CAAC,aAAa,CAAC,2BACd,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;gEACzB;;;;;;;;;;;;4CAIJ,CAAC,aAAa,4BACb,6LAAC;gDAAY,WAAU;0DACpB,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;oDACrB,MAAM,aAAa,OAAO,IAAI;oDAC9B,qBACE,6LAAC;wDAAuB,WAAU;kEAChC,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAW,WAAU;;;;;;8EACtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA4B,OAAO,IAAI;;;;;;sFACrD,6LAAC;4EAAE,WAAU;sFAAsC,OAAO,WAAW;;;;;;sFACrE,6LAAC;4EAAI,WAAU;sFACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,6LAAC;oFAAgB,SAAQ;oFAAU,WAAU;8FAC1C;mFADS;;;;;;;;;;;;;;;;;;;;;;uDARX,OAAO,IAAI;;;;;gDAiB1B;;;;;;;uCAxCK,SAAS,IAAI;;;;;gCA6C5B;;;;;;;wBAID,CAAC,2BACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAClF,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAInB,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;wCAAqB,WAAU;kDAC9B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA4B,OAAO,IAAI;;;;;;sEACrD,6LAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,6LAAC;oEAAgB,SAAQ;oEAAU,WAAU;8EAC1C;mEADS;;;;;;;;;;;;;;;;;;;;;;uCAPX,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBpC;GA9JgB;KAAA", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/workspace/workspace-module.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSortable } from \"@dnd-kit/sortable\"\nimport { CSS } from \"@dnd-kit/utilities\"\nimport { Button } from \"@/components/ui/button\"\nimport { GripVertical, X, Settings, Play } from \"lucide-react\"\n\ninterface Module {\n  id: string\n  name: string\n  description: string\n  type: string\n}\n\ninterface WorkspaceModuleProps {\n  module: Module\n  index: number\n  onRemove: (moduleId: string) => void\n}\n\nexport function WorkspaceModule({ module, index, onRemove }: WorkspaceModuleProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: module.id })\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  }\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      className={`group relative bg-card border border-border rounded-lg p-4 ${\n        isDragging ? \"opacity-50\" : \"\"\n      }`}\n    >\n      {/* Module Header */}\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center gap-3\">\n          <div\n            {...attributes}\n            {...listeners}\n            className=\"cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded\"\n          >\n            <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\n          </div>\n          <div>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-xs bg-primary/20 text-primary px-2 py-1 rounded\">\n                #{index + 1}\n              </span>\n              <h3 className=\"font-medium\">{module.name}</h3>\n            </div>\n            <p className=\"text-sm text-muted-foreground mt-1\">\n              {module.description}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <Button variant=\"ghost\" size=\"sm\">\n            <Settings className=\"h-4 w-4\" />\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Play className=\"h-4 w-4\" />\n          </Button>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\"\n            onClick={() => onRemove(module.id)}\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Module Content */}\n      <div className=\"space-y-3\">\n        {/* Input Section */}\n        <div>\n          <label className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n            Input\n          </label>\n          <div className=\"mt-1 p-3 bg-muted/50 rounded border border-dashed border-border\">\n            <textarea\n              placeholder=\"Enter your prompt or connect from previous module...\"\n              className=\"w-full bg-transparent border-none outline-none resize-none text-sm\"\n              rows={2}\n            />\n          </div>\n        </div>\n\n        {/* Output Section */}\n        <div>\n          <label className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n            Output\n          </label>\n          <div className=\"mt-1 p-3 bg-background/50 rounded border border-border min-h-[60px]\">\n            <div className=\"text-sm text-muted-foreground italic\">\n              Output will appear here after execution...\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Connection Indicator */}\n      {index > 0 && (\n        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n          <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n          <div className=\"w-px h-4 bg-border mx-auto\"></div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAoBO,SAAS,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAwB;;IAC/E,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,OAAO,EAAE;IAAC;IAEhC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAC,2DAA2D,EACrE,aAAa,eAAe,IAC5B;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACE,GAAG,UAAU;gCACb,GAAG,SAAS;gCACb,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAE1B,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAuD;oDACnE,QAAQ;;;;;;;0DAEZ,6LAAC;gDAAG,WAAU;0DAAe,OAAO,IAAI;;;;;;;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,OAAO,EAAE;0CAEjC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAoE;;;;;;0CAGrF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,aAAY;oCACZ,WAAU;oCACV,MAAM;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAoE;;;;;;0CAGrF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;YAQ3D,QAAQ,mBACP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAKzB;GArGgB;;QAQV,sKAAA,CAAA,cAAW;;;KARD", "debugId": null}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/workspace/workspace.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  closestCenter,\n} from \"@dnd-kit/core\"\nimport {\n  SortableContext,\n  verticalListSortingStrategy,\n} from \"@dnd-kit/sortable\"\nimport { WorkspaceModule } from \"./workspace-module\"\nimport { Button } from \"@/components/ui/button\"\nimport { Play, Plus } from \"lucide-react\"\n\ninterface Module {\n  id: string\n  name: string\n  description: string\n  type: string\n}\n\nexport function Workspace() {\n  const [modules, setModules] = useState<Module[]>([])\n  const [activeModule, setActiveModule] = useState<Module | null>(null)\n\n  const handleDragStart = (event: DragStartEvent) => {\n    const module = modules.find(m => m.id === event.active.id)\n    setActiveModule(module || null)\n  }\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    setActiveModule(null)\n    \n    const { active, over } = event\n    \n    if (!over) return\n\n    // Handle dropping from sidebar\n    if (typeof active.id === \"string\" && active.id.startsWith(\"sidebar-\")) {\n      try {\n        const moduleData = JSON.parse(active.data.current?.moduleData || \"{}\")\n        const newModule: Module = {\n          id: `module-${Date.now()}`,\n          name: moduleData.name || \"New Module\",\n          description: moduleData.description || \"\",\n          type: \"custom\"\n        }\n        setModules(prev => [...prev, newModule])\n      } catch (error) {\n        console.error(\"Error parsing module data:\", error)\n      }\n      return\n    }\n\n    // Handle reordering existing modules\n    if (active.id !== over.id) {\n      setModules((items) => {\n        const oldIndex = items.findIndex(item => item.id === active.id)\n        const newIndex = items.findIndex(item => item.id === over.id)\n        \n        const newItems = [...items]\n        const [reorderedItem] = newItems.splice(oldIndex, 1)\n        newItems.splice(newIndex, 0, reorderedItem)\n        \n        return newItems\n      })\n    }\n  }\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault()\n    \n    try {\n      const moduleData = JSON.parse(event.dataTransfer.getData(\"text/plain\"))\n      const newModule: Module = {\n        id: `module-${Date.now()}`,\n        name: moduleData.name,\n        description: moduleData.description,\n        type: \"library\"\n      }\n      setModules(prev => [...prev, newModule])\n    } catch (error) {\n      console.error(\"Error handling drop:\", error)\n    }\n  }\n\n  const removeModule = (moduleId: string) => {\n    setModules(prev => prev.filter(m => m.id !== moduleId))\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col\">\n      {/* Workspace Header */}\n      <div className=\"border-b border-border bg-card/30 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-lg font-semibold\">Prompt Chain Workspace</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Drag modules here to build your prompt chain\n            </p>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Module\n            </Button>\n            <Button variant=\"catalyst\" size=\"sm\" disabled={modules.length === 0}>\n              <Play className=\"h-4 w-4 mr-2\" />\n              Run Chain\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Drop Zone */}\n      <div\n        className=\"flex-1 p-6\"\n        onDrop={handleDrop}\n        onDragOver={(e) => e.preventDefault()}\n      >\n        <DndContext\n          collisionDetection={closestCenter}\n          onDragStart={handleDragStart}\n          onDragEnd={handleDragEnd}\n        >\n          <SortableContext items={modules} strategy={verticalListSortingStrategy}>\n            {modules.length === 0 ? (\n              <div className=\"flex-1 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"w-24 h-24 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center\">\n                    <Plus className=\"h-8 w-8 text-muted-foreground\" />\n                  </div>\n                  <h3 className=\"text-lg font-medium mb-2\">Empty Workspace</h3>\n                  <p className=\"text-muted-foreground max-w-md\">\n                    Drag modules from the sidebar to start building your prompt chain.\n                    Connect modules to create powerful AI workflows.\n                  </p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {modules.map((module, index) => (\n                  <WorkspaceModule\n                    key={module.id}\n                    module={module}\n                    index={index}\n                    onRemove={removeModule}\n                  />\n                ))}\n              </div>\n            )}\n          </SortableContext>\n\n          <DragOverlay>\n            {activeModule ? (\n              <div className=\"p-4 bg-card border border-border rounded-lg shadow-lg\">\n                <div className=\"font-medium\">{activeModule.name}</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {activeModule.description}\n                </div>\n              </div>\n            ) : null}\n          </DragOverlay>\n        </DndContext>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAIA;AACA;AACA;AAAA;;;AAhBA;;;;;;;AAyBO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE;QACzD,gBAAgB,UAAU;IAC5B;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAEhB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,MAAM;QAEX,+BAA+B;QAC/B,IAAI,OAAO,OAAO,EAAE,KAAK,YAAY,OAAO,EAAE,CAAC,UAAU,CAAC,aAAa;YACrE,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,cAAc;gBACjE,MAAM,YAAoB;oBACxB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;oBAC1B,MAAM,WAAW,IAAI,IAAI;oBACzB,aAAa,WAAW,WAAW,IAAI;oBACvC,MAAM;gBACR;gBACA,WAAW,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YACzC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;YACA;QACF;QAEA,qCAAqC;QACrC,IAAI,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACzB,WAAW,CAAC;gBACV,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gBAC9D,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;gBAE5D,MAAM,WAAW;uBAAI;iBAAM;gBAC3B,MAAM,CAAC,cAAc,GAAG,SAAS,MAAM,CAAC,UAAU;gBAClD,SAAS,MAAM,CAAC,UAAU,GAAG;gBAE7B,OAAO;YACT;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QAEpB,IAAI;YACF,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,YAAY,CAAC,OAAO,CAAC;YACzD,MAAM,YAAoB;gBACxB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,MAAM;YACR;YACA,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAW,MAAK;oCAAK,UAAU,QAAQ,MAAM,KAAK;;sDAChE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,6LAAC;gBACC,WAAU;gBACV,QAAQ;gBACR,YAAY,CAAC,IAAM,EAAE,cAAc;0BAEnC,cAAA,6LAAC,8JAAA,CAAA,aAAU;oBACT,oBAAoB,8JAAA,CAAA,gBAAa;oBACjC,aAAa;oBACb,WAAW;;sCAEX,6LAAC,sKAAA,CAAA,kBAAe;4BAAC,OAAO;4BAAS,UAAU,sKAAA,CAAA,8BAA2B;sCACnE,QAAQ,MAAM,KAAK,kBAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;sDAAiC;;;;;;;;;;;;;;;;qDAOlD,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,yJAAA,CAAA,kBAAe;wCAEd,QAAQ;wCACR,OAAO;wCACP,UAAU;uCAHL,OAAO,EAAE;;;;;;;;;;;;;;;sCAUxB,6LAAC,8JAAA,CAAA,cAAW;sCACT,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAe,aAAa,IAAI;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;kDACZ,aAAa,WAAW;;;;;;;;;;;uCAG3B;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GAlJgB;KAAA", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from \"@/components/ui/tabs\";\nimport { <PERSON>, Zap, Settings } from \"lucide-react\";\nimport { Head<PERSON> } from \"@/components/layout/header\";\nimport { Sidebar } from \"@/components/layout/sidebar\";\nimport { Workspace } from \"@/components/workspace/workspace\";\nimport { OutputPanel } from \"@/components/output/output-panel\";\nimport { SettingsPanel } from \"@/components/settings/settings-panel\";\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState(\"workspace\");\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-catalyst-dark text-foreground\">\n      <Header />\n\n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        {/* Module Library Sidebar */}\n        <Sidebar\n          collapsed={sidebarCollapsed}\n          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\n        />\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"flex-1 flex flex-col\">\n            <div className=\"border-b border-border px-4 py-2\">\n              <TabsList className=\"bg-muted/20\">\n                <TabsTrigger value=\"workspace\" className=\"flex items-center gap-2\">\n                  <Link className=\"w-4 h-4\" />\n                  Workspace\n                </TabsTrigger>\n                <TabsTrigger value=\"output\" className=\"flex items-center gap-2\">\n                  <Zap className=\"w-4 h-4\" />\n                  Output\n                </TabsTrigger>\n                <TabsTrigger value=\"settings\" className=\"flex items-center gap-2\">\n                  <Settings className=\"w-4 h-4\" />\n                  Settings\n                </TabsTrigger>\n              </TabsList>\n            </div>\n\n            <div className=\"flex-1 overflow-hidden\">\n              <TabsContent value=\"workspace\" className=\"h-full m-0\">\n                <Workspace />\n              </TabsContent>\n\n              <TabsContent value=\"output\" className=\"h-full m-0\">\n                <OutputPanel />\n              </TabsContent>\n\n              <TabsContent value=\"settings\" className=\"h-full m-0\">\n                <SettingsPanel />\n              </TabsContent>\n            </div>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;AAPA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0IAAA,CAAA,UAAO;wBACN,WAAW;wBACX,kBAAkB,IAAM,oBAAoB,CAAC;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAS,WAAU;;0DAClB,6LAAC;gDAAY,OAAM;gDAAY,WAAU;;kEACvC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,6LAAC;gDAAY,OAAM;gDAAS,WAAU;;kEACpC,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG7B,6LAAC;gDAAY,OAAM;gDAAW,WAAU;;kEACtC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;8CAMtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAY,OAAM;4CAAY,WAAU;sDACvC,cAAA,6LAAC,+IAAA,CAAA,YAAS;;;;;;;;;;sDAGZ,6LAAC;4CAAY,OAAM;4CAAS,WAAU;sDACpC,cAAA,6LAAC;;;;;;;;;;sDAGH,6LAAC;4CAAY,OAAM;4CAAW,WAAU;sDACtC,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GArDwB;KAAA", "debugId": null}}]}