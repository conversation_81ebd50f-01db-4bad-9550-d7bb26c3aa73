
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Play, 
  Plus, 
  Link2, 
  Settings, 
  ArrowRight, 
  Zap,
  FileText,
  MessageSquare,
  Code,
  Trash2,
  Edit,
  MoreVertical,
  Copy,
  Download,
  Share,
  Archive,
  Eye,
  EyeOff
} from "lucide-react";

interface PromptModule {
  id: string;
  name: string;
  type: 'system' | 'user' | 'chain';
  content: string;
  model?: string;
  connected?: boolean;
  enabled?: boolean;
}

const PromptWorkspace = () => {
  const [modules, setModules] = useState<PromptModule[]>([
    {
      id: '1',
      name: 'System Context',
      type: 'system',
      content: 'You are an expert writing assistant. Help the user create high-quality content.',
      model: 'GPT-4o',
      enabled: true
    },
    {
      id: '2',
      name: 'User Input',
      type: 'user',
      content: 'Write a blog post about {{topic}} in a {{tone}} tone for {{audience}}.',
      connected: true,
      enabled: true
    },
    {
      id: '3',
      name: 'Chain of Draft',
      type: 'chain',
      content: 'First create an outline, then write a draft, then refine and polish the content.',
      enabled: false
    }
  ]);

  const [selectedModel, setSelectedModel] = useState("gpt-4o");
  const [isRunning, setIsRunning] = useState(false);

  const addModule = () => {
    const newModule: PromptModule = {
      id: Date.now().toString(),
      name: 'New Module',
      type: 'user',
      content: 'Enter your prompt here...',
      enabled: true
    };
    setModules([...modules, newModule]);
  };

  const removeModule = (id: string) => {
    setModules(modules.filter(m => m.id !== id));
  };

  const updateModule = (id: string, updates: Partial<PromptModule>) => {
    setModules(modules.map(m => m.id === id ? { ...m, ...updates } : m));
  };

  const duplicateModule = (id: string) => {
    const module = modules.find(m => m.id === id);
    if (module) {
      const newModule = {
        ...module,
        id: Date.now().toString(),
        name: `${module.name} (Copy)`
      };
      setModules([...modules, newModule]);
    }
  };

  const toggleModuleEnabled = (id: string) => {
    updateModule(id, { enabled: !modules.find(m => m.id === id)?.enabled });
  };

  const runChain = async () => {
    setIsRunning(true);
    // Simulate API call
    setTimeout(() => setIsRunning(false), 3000);
  };

  return (
    <div className="h-full flex">
      {/* Chain Builder */}
      <div className="flex-1 p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold gradient-text">Prompt Chain Workspace</h2>
            <p className="text-sm text-muted-foreground">Drag, drop, and chain your prompt modules</p>
          </div>
          
          <div className="flex items-center gap-3">
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-popover border catalyst-border z-50">
                <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                <SelectItem value="claude-3.5">Claude 3.5 Sonnet</SelectItem>
                <SelectItem value="mistral-large">Mistral Large</SelectItem>
                <SelectItem value="llama-3.1">Llama 3.1 70B</SelectItem>
                <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={addModule} variant="outline" size="sm" className="gap-2">
              <Plus className="w-4 h-4" />
              Add Module
            </Button>
            
            <Button 
              onClick={runChain} 
              disabled={isRunning}
              className="gap-2 bg-catalyst-gradient hover:opacity-90"
            >
              {isRunning ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isRunning ? 'Running...' : 'Run Chain'}
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-4">
            {modules.map((module, index) => (
              <div key={module.id} className="relative">
                <Card className={`prompt-panel p-6 transition-all duration-200 ${
                  module.enabled ? 'hover:catalyst-glow' : 'opacity-60'
                }`}>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        module.type === 'system' ? 'bg-catalyst-blue' :
                        module.type === 'user' ? 'bg-catalyst-purple' :
                        'bg-gradient-to-r from-catalyst-blue to-catalyst-purple'
                      }`} />
                      <div>
                        <input
                          value={module.name}
                          onChange={(e) => updateModule(module.id, { name: e.target.value })}
                          className="font-medium bg-transparent border-none outline-none text-foreground"
                        />
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {module.type}
                          </Badge>
                          {module.model && (
                            <Badge variant="secondary" className="text-xs">
                              {module.model}
                            </Badge>
                          )}
                          {!module.enabled && (
                            <Badge variant="destructive" className="text-xs">
                              Disabled
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="p-2"
                        onClick={() => toggleModuleEnabled(module.id)}
                      >
                        {module.enabled ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                      </Button>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="p-2">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-popover border catalyst-border z-50" align="end">
                          <DropdownMenuLabel>Module Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => duplicateModule(module.id)}>
                            <Copy className="w-4 h-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Share className="w-4 h-4 mr-2" />
                            Share
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="w-4 h-4 mr-2" />
                            Export
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Archive className="w-4 h-4 mr-2" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Settings className="w-4 h-4 mr-2" />
                            Settings
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => removeModule(module.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  
                  <Textarea
                    value={module.content}
                    onChange={(e) => updateModule(module.id, { content: e.target.value })}
                    placeholder="Enter your prompt content..."
                    className="min-h-24 bg-muted/10 border-muted resize-none"
                    disabled={!module.enabled}
                  />
                  
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-xs text-muted-foreground">
                      {module.content.length} characters
                    </div>
                    <Button variant="ghost" size="sm" className="gap-2 text-xs">
                      <Edit className="w-3 h-3" />
                      Edit in PromptForge
                    </Button>
                  </div>
                </Card>
                
                {/* Connection Arrow */}
                {index < modules.length - 1 && (
                  <div className="flex justify-center py-2">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <div className="w-8 h-px bg-gradient-to-r from-catalyst-blue to-catalyst-purple" />
                      <ArrowRight className="w-4 h-4" />
                      <div className="w-8 h-px bg-gradient-to-r from-catalyst-blue to-catalyst-purple" />
                    </div>
                  </div>
                )}
              </div>
            ))}

            {modules.length === 0 && (
              <Card className="prompt-panel p-12 text-center">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-catalyst-gradient rounded-full flex items-center justify-center mx-auto">
                    <Link2 className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">No modules in your chain</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Start by adding a prompt module or drag one from the sidebar
                    </p>
                    <Button onClick={addModule} className="gap-2">
                      <Plus className="w-4 h-4" />
                      Add Your First Module
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export default PromptWorkspace;
