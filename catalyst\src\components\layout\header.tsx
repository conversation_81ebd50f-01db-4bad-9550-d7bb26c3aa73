"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Settings, Save, Download, Upload } from "lucide-react"

export function Header() {
  return (
    <header className="border-b border-border bg-card/50 backdrop-blur-sm">
      <div className="flex h-16 items-center justify-between px-6">
        {/* Logo and Title */}
        <div className="flex items-center gap-3">
          <Image
            src="/catalyst-logo.png"
            alt="Catalyst"
            width={32}
            height={32}
            className="catalyst-glow"
          />
          <div>
            <h1 className="text-xl font-bold catalyst-gradient-text">
              Catalyst
            </h1>
            <p className="text-xs text-muted-foreground">
              Prompt Engineering Ecosystem
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Upload className="h-4 w-4" />
            Import
          </Button>
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="ghost" size="sm">
            <Save className="h-4 w-4" />
            Save
          </Button>
          <Button variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>
    </header>
  )
}
