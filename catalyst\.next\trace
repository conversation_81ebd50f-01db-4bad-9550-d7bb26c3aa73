[{"name": "hot-reloader", "duration": 134, "timestamp": 72188614711, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750924831488, "traceId": "2e9d3e5374f8451f"}, {"name": "setup-dev-bundler", "duration": 1347395, "timestamp": 72188015353, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750924830889, "traceId": "2e9d3e5374f8451f"}, {"name": "run-instrumentation-hook", "duration": 40, "timestamp": 72189619344, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750924832493, "traceId": "2e9d3e5374f8451f"}, {"name": "start-dev-server", "duration": 3087694, "timestamp": 72186568689, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "8777555968", "memory.totalMem": "16912334848", "memory.heapSizeLimit": "8506048512", "memory.rss": "170061824", "memory.heapTotal": "96915456", "memory.heapUsed": "65613560"}, "startTime": 1750924829442, "traceId": "2e9d3e5374f8451f"}, {"name": "compile-path", "duration": 5754285, "timestamp": 72241689352, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750924884563, "traceId": "2e9d3e5374f8451f"}, {"name": "ensure-page", "duration": 5817463, "timestamp": 72241687735, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750924884561, "traceId": "2e9d3e5374f8451f"}]