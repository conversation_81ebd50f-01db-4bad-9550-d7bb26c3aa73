
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { 
  ChevronLeft, 
  ChevronRight, 
  Search, 
  Plus, 
  Zap, 
  FileText, 
  MessageSquare, 
  Code, 
  Palette,
  Brain,
  Link2,
  Download
} from "lucide-react";

interface ModuleSidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const prebuiltEngines = [
  { id: 1, name: "Writer Engine", icon: FileText, description: "Outline → Expand → Style", badge: "3 steps" },
  { id: 2, name: "Interview Simulator", icon: MessageSquare, description: "Role-play Q&A trainer", badge: "Dynamic" },
  { id: 3, name: "Debug Console", icon: Code, description: "Analyze → Suggest Fix", badge: "2 steps" },
];

const savedModules = [
  { id: 4, name: "Marketing Copy", icon: Zap, tags: ["writing", "marketing"] },
  { id: 5, name: "Code Review", icon: Code, tags: ["coding", "analysis"] },
  { id: 6, name: "Research Summary", icon: FileText, tags: ["research", "synthesis"] },
];

const ModuleSidebar = ({ collapsed, onToggleCollapse }: ModuleSidebarProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <div className={`${collapsed ? 'w-16' : 'w-80'} border-r border-border bg-card/20 backdrop-blur-sm transition-all duration-300 flex flex-col`}>
      {/* Sidebar Header */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        {!collapsed && (
          <div>
            <h2 className="font-semibold text-sm">Module Library</h2>
            <p className="text-xs text-muted-foreground">Engines & Modules</p>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="p-2"
        >
          {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Search */}
          {!collapsed && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search modules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-muted/20"
              />
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-2">
            {!collapsed && <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Quick Start</h3>}
            
            <Button 
              variant="outline" 
              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}
            >
              <Brain className="w-4 h-4 text-catalyst-purple" />
              {!collapsed && "Prompt4Me Assistant"}
            </Button>
            
            <Button 
              variant="outline" 
              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}
            >
              <Palette className="w-4 h-4 text-catalyst-teal" />
              {!collapsed && "PromptForge Lite"}
            </Button>
          </div>

          <Separator />

          {/* Prompt Engines */}
          <div className="space-y-3">
            {!collapsed && <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Prompt Engines</h3>}
            
            {prebuiltEngines.map((engine) => (
              <Card key={engine.id} className="module-card cursor-pointer p-3">
                <div className="flex items-start gap-3">
                  <engine.icon className="w-5 h-5 text-catalyst-blue mt-0.5" />
                  {!collapsed && (
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-sm">{engine.name}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {engine.badge}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{engine.description}</p>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>

          <Separator />

          {/* Saved Modules */}
          <div className="space-y-3">
            {!collapsed && (
              <div className="flex items-center justify-between">
                <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Saved Modules</h3>
                <Button variant="ghost" size="sm" className="p-1">
                  <Plus className="w-3 h-3" />
                </Button>
              </div>
            )}
            
            {savedModules.map((module) => (
              <Card key={module.id} className="module-card cursor-pointer p-3">
                <div className="flex items-start gap-3">
                  <module.icon className="w-4 h-4 text-catalyst-purple mt-0.5" />
                  {!collapsed && (
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm mb-1">{module.name}</h4>
                      <div className="flex flex-wrap gap-1">
                        {module.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default ModuleSidebar;
