"use client"

import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Button } from "@/components/ui/button"
import { GripVertical, X, Settings, Play } from "lucide-react"

interface Module {
  id: string
  name: string
  description: string
  type: string
}

interface WorkspaceModuleProps {
  module: Module
  index: number
  onRemove: (moduleId: string) => void
}

export function WorkspaceModule({ module, index, onRemove }: WorkspaceModuleProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-card border border-border rounded-lg p-4 ${
        isDragging ? "opacity-50" : ""
      }`}
    >
      {/* Module Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded">
                #{index + 1}
              </span>
              <h3 className="font-medium">{module.name}</h3>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {module.description}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Play className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => onRemove(module.id)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Module Content */}
      <div className="space-y-3">
        {/* Input Section */}
        <div>
          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            Input
          </label>
          <div className="mt-1 p-3 bg-muted/50 rounded border border-dashed border-border">
            <textarea
              placeholder="Enter your prompt or connect from previous module..."
              className="w-full bg-transparent border-none outline-none resize-none text-sm"
              rows={2}
            />
          </div>
        </div>

        {/* Output Section */}
        <div>
          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            Output
          </label>
          <div className="mt-1 p-3 bg-background/50 rounded border border-border min-h-[60px]">
            <div className="text-sm text-muted-foreground italic">
              Output will appear here after execution...
            </div>
          </div>
        </div>
      </div>

      {/* Connection Indicator */}
      {index > 0 && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <div className="w-2 h-2 bg-primary rounded-full"></div>
          <div className="w-px h-4 bg-border mx-auto"></div>
        </div>
      )}
    </div>
  )
}
