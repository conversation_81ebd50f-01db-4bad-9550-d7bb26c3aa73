"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, Zap, Settings } from "lucide-react";
import { Head<PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { Workspace } from "@/components/workspace/workspace";
import { OutputPanel } from "@/components/output/output-panel";
import { SettingsPanel } from "@/components/settings/settings-panel";

export default function Home() {
  const [activeTab, setActiveTab] = useState("workspace");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className="min-h-screen bg-catalyst-dark text-foreground">
      <Header />

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Module Library Sidebar */}
        <Sidebar
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="border-b border-border px-4 py-2">
              <TabsList className="bg-muted/20">
                <TabsTrigger value="workspace" className="flex items-center gap-2">
                  <Link className="w-4 h-4" />
                  Workspace
                </TabsTrigger>
                <TabsTrigger value="output" className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Output
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="workspace" className="h-full m-0">
                <Workspace />
              </TabsContent>

              <TabsContent value="output" className="h-full m-0">
                <OutputPanel />
              </TabsContent>

              <TabsContent value="settings" className="h-full m-0">
                <SettingsPanel />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

