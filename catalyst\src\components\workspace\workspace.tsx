"use client"

import { useState } from "react"
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  closestCenter,
} from "@dnd-kit/core"
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { WorkspaceModule } from "./workspace-module"
import { Button } from "@/components/ui/button"
import { Play, Plus } from "lucide-react"

interface Module {
  id: string
  name: string
  description: string
  type: string
}

export function Workspace() {
  const [modules, setModules] = useState<Module[]>([])
  const [activeModule, setActiveModule] = useState<Module | null>(null)

  const handleDragStart = (event: DragStartEvent) => {
    const module = modules.find(m => m.id === event.active.id)
    setActiveModule(module || null)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveModule(null)
    
    const { active, over } = event
    
    if (!over) return

    // Handle dropping from sidebar
    if (typeof active.id === "string" && active.id.startsWith("sidebar-")) {
      try {
        const moduleData = JSON.parse(active.data.current?.moduleData || "{}")
        const newModule: Module = {
          id: `module-${Date.now()}`,
          name: moduleData.name || "New Module",
          description: moduleData.description || "",
          type: "custom"
        }
        setModules(prev => [...prev, newModule])
      } catch (error) {
        console.error("Error parsing module data:", error)
      }
      return
    }

    // Handle reordering existing modules
    if (active.id !== over.id) {
      setModules((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id)
        const newIndex = items.findIndex(item => item.id === over.id)
        
        const newItems = [...items]
        const [reorderedItem] = newItems.splice(oldIndex, 1)
        newItems.splice(newIndex, 0, reorderedItem)
        
        return newItems
      })
    }
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    
    try {
      const moduleData = JSON.parse(event.dataTransfer.getData("text/plain"))
      const newModule: Module = {
        id: `module-${Date.now()}`,
        name: moduleData.name,
        description: moduleData.description,
        type: "library"
      }
      setModules(prev => [...prev, newModule])
    } catch (error) {
      console.error("Error handling drop:", error)
    }
  }

  const removeModule = (moduleId: string) => {
    setModules(prev => prev.filter(m => m.id !== moduleId))
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Workspace Header */}
      <div className="border-b border-border bg-card/30 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Prompt Chain Workspace</h2>
            <p className="text-sm text-muted-foreground">
              Drag modules here to build your prompt chain
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Module
            </Button>
            <Button variant="catalyst" size="sm" disabled={modules.length === 0}>
              <Play className="h-4 w-4 mr-2" />
              Run Chain
            </Button>
          </div>
        </div>
      </div>

      {/* Drop Zone */}
      <div
        className="flex-1 p-6"
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
      >
        <DndContext
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={modules} strategy={verticalListSortingStrategy}>
            {modules.length === 0 ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
                    <Plus className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">Empty Workspace</h3>
                  <p className="text-muted-foreground max-w-md">
                    Drag modules from the sidebar to start building your prompt chain.
                    Connect modules to create powerful AI workflows.
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {modules.map((module, index) => (
                  <WorkspaceModule
                    key={module.id}
                    module={module}
                    index={index}
                    onRemove={removeModule}
                  />
                ))}
              </div>
            )}
          </SortableContext>

          <DragOverlay>
            {activeModule ? (
              <div className="p-4 bg-card border border-border rounded-lg shadow-lg">
                <div className="font-medium">{activeModule.name}</div>
                <div className="text-sm text-muted-foreground">
                  {activeModule.description}
                </div>
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>
    </div>
  )
}
