
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  Copy, 
  Share, 
  RefreshCw, 
  Clock, 
  Zap,
  FileText,
  Code,
  Eye
} from "lucide-react";

const OutputPanel = () => {
  const [activeTab, setActiveTab] = useState("live");
  const [isStreaming, setIsStreaming] = useState(false);
  
  const mockStreamingText = `# The Future of AI-Powered Content Creation

Artificial Intelligence has revolutionized the way we approach content creation, offering unprecedented opportunities for creativity and efficiency. In this comprehensive guide, we'll explore how AI tools are transforming the creative landscape...

## Key Benefits of AI in Content Creation

1. **Speed and Efficiency**: AI can generate content at remarkable speeds
2. **Consistency**: Maintains tone and style across large volumes
3. **Ideation Support**: Helps overcome creative blocks
4. **Personalization**: Adapts content for different audiences

The integration of AI in creative workflows isn't about replacing human creativity—it's about amplifying it.`;

  const exportFormats = [
    { name: "Markdown", ext: ".md", icon: FileText },
    { name: "Plain Text", ext: ".txt", icon: FileText },
    { name: "JSON", ext: ".json", icon: Code },
    { name: "PromptX", ext: ".promptx", icon: Zap },
  ];

  const sessionHistory = [
    { id: 1, timestamp: "2 min ago", model: "GPT-4o", tokens: 1250, title: "Blog Post Generation" },
    { id: 2, timestamp: "5 min ago", model: "Claude 3.5", tokens: 890, title: "Code Review" },
    { id: 3, timestamp: "12 min ago", model: "GPT-4o", tokens: 2100, title: "Marketing Copy" },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold gradient-text">AI Output Stream</h2>
            <p className="text-sm text-muted-foreground">Live results from your prompt chains</p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              GPT-4o • 1,247 tokens
            </div>
            
            <div className="flex items-center gap-2">
              {exportFormats.map((format) => (
                <Button key={format.name} variant="outline" size="sm" className="gap-2">
                  <format.icon className="w-4 h-4" />
                  {format.ext}
                </Button>
              ))}
            </div>
            
            <Button variant="outline" size="sm" className="gap-2">
              <Copy className="w-4 h-4" />
              Copy
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-6 py-3 border-b border-border">
          <TabsList className="bg-muted/20">
            <TabsTrigger value="live" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Live Output
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Session History
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="live" className="h-full m-0">
            <div className="h-full p-6">
              <Card className="h-full prompt-panel">
                <div className="p-6 h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="secondary" className="gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      Streaming Active
                    </Badge>
                    <Button variant="ghost" size="sm" className="gap-2">
                      <RefreshCw className="w-4 h-4" />
                      Regenerate
                    </Button>
                  </div>
                  
                  <ScrollArea className="flex-1">
                    <div className="prose prose-invert max-w-none">
                      <div className="whitespace-pre-wrap text-sm leading-relaxed">
                        {mockStreamingText}
                        <span className="animate-pulse">|</span>
                      </div>
                    </div>
                  </ScrollArea>
                  
                  <div className="mt-4 pt-4 border-t border-border flex items-center justify-between text-xs text-muted-foreground">
                    <div>Generated in 2.3s • 1,247 tokens • ~$0.025</div>
                    <div className="flex items-center gap-4">
                      <span>Quality: High</span>
                      <span>Coherence: 98%</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="h-full m-0">
            <div className="h-full p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Recent Generations</h3>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Download className="w-4 h-4" />
                    Export Session
                  </Button>
                </div>

                <div className="space-y-3">
                  {sessionHistory.map((item) => (
                    <Card key={item.id} className="module-card cursor-pointer">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{item.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {item.model}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{item.timestamp}</span>
                          <span>{item.tokens} tokens</span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default OutputPanel;
