
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Settings, 
  Brain, 
  Zap, 
  Database, 
  Globe, 
  Shield,
  Palette,
  BarChart3
} from "lucide-react";

const SettingsPanel = () => {
  const [temperature, setTemperature] = useState([0.7]);
  const [maxTokens, setMaxTokens] = useState([2048]);
  const [enableMemory, setEnableMemory] = useState(true);
  const [streamOutput, setStreamOutput] = useState(true);

  const models = [
    { 
      name: "GPT-4o", 
      provider: "OpenAI",
      strengths: ["Creative reasoning", "Code generation", "Analysis"],
      weaknesses: ["Can be verbose", "Higher cost"],
      bestFor: "Complex reasoning, creative writing"
    },
    { 
      name: "Claude 3.5 Sonnet", 
      provider: "Anthropic",
      strengths: ["Safety", "Nuanced responses", "Long context"],
      weaknesses: ["Conservative", "Limited availability"],
      bestFor: "Safe content, detailed analysis"
    },
    { 
      name: "Mistral Large", 
      provider: "Mistral AI",
      strengths: ["Efficiency", "Multilingual", "Cost-effective"],
      weaknesses: ["Less creative", "Newer model"],
      bestFor: "Production workloads, multilingual tasks"
    }
  ];

  return (
    <div className="h-full">
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <Settings className="w-6 h-6 text-catalyst-purple" />
          <div>
            <h2 className="text-xl font-semibold gradient-text">Settings & Configuration</h2>
            <p className="text-sm text-muted-foreground">Customize your Catalyst experience</p>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[calc(100%-100px)]">
        <div className="p-6 space-y-8">
          {/* Model Configuration */}
          <Card className="prompt-panel p-6">
            <div className="flex items-center gap-3 mb-6">
              <Brain className="w-5 h-5 text-catalyst-blue" />
              <h3 className="font-semibold">Model Configuration</h3>
            </div>

            <div className="space-y-6">
              <div>
                <Label className="text-sm font-medium mb-3 block">Available Models</Label>
                <div className="grid gap-4">
                  {models.map((model) => (
                    <Card key={model.name} className="module-card p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium flex items-center gap-2">
                            {model.name}
                            <Badge variant="outline" className="text-xs">
                              {model.provider}
                            </Badge>
                          </h4>
                          <p className="text-xs text-muted-foreground mt-1">{model.bestFor}</p>
                        </div>
                        <Button variant="outline" size="sm">Select</Button>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4 text-xs">
                        <div>
                          <p className="font-medium text-green-400 mb-1">Strengths</p>
                          <ul className="space-y-0.5 text-muted-foreground">
                            {model.strengths.map((strength) => (
                              <li key={strength}>• {strength}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <p className="font-medium text-yellow-400 mb-1">Considerations</p>
                          <ul className="space-y-0.5 text-muted-foreground">
                            {model.weaknesses.map((weakness) => (
                              <li key={weakness}>• {weakness}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Generation Parameters */}
          <Card className="prompt-panel p-6">
            <div className="flex items-center gap-3 mb-6">
              <Zap className="w-5 h-5 text-catalyst-purple" />
              <h3 className="font-semibold">Generation Parameters</h3>
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-3">
                  <Label className="text-sm font-medium">Temperature</Label>
                  <Badge variant="secondary" className="text-xs">{temperature[0]}</Badge>
                </div>
                <Slider
                  value={temperature}
                  onValueChange={setTemperature}
                  min={0}
                  max={2}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Higher values make output more creative but less predictable
                </p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-3">
                  <Label className="text-sm font-medium">Max Tokens</Label>
                  <Badge variant="secondary" className="text-xs">{maxTokens[0]}</Badge>
                </div>
                <Slider
                  value={maxTokens}
                  onValueChange={setMaxTokens}
                  min={100}
                  max={4000}
                  step={100}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Maximum length of generated response
                </p>
              </div>
            </div>
          </Card>

          {/* System Preferences */}
          <Card className="prompt-panel p-6">
            <div className="flex items-center gap-3 mb-6">
              <Database className="w-5 h-5 text-catalyst-teal" />
              <h3 className="font-semibold">System Preferences</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Session Memory</Label>
                  <p className="text-xs text-muted-foreground">Remember context across prompts</p>
                </div>
                <Switch checked={enableMemory} onCheckedChange={setEnableMemory} />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Stream Output</Label>
                  <p className="text-xs text-muted-foreground">Show results as they're generated</p>
                </div>
                <Switch checked={streamOutput} onCheckedChange={setStreamOutput} />
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium mb-3 block">Default Export Format</Label>
                <Select defaultValue="markdown">
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border catalyst-border">
                    <SelectItem value="markdown">Markdown (.md)</SelectItem>
                    <SelectItem value="text">Plain Text (.txt)</SelectItem>
                    <SelectItem value="json">JSON (.json)</SelectItem>
                    <SelectItem value="promptx">PromptX (.promptx)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>

          {/* API & Integration */}
          <Card className="prompt-panel p-6">
            <div className="flex items-center gap-3 mb-6">
              <Globe className="w-5 h-5 text-catalyst-blue" />
              <h3 className="font-semibold">API & Integration</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/10 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">OpenRouter</p>
                    <p className="text-xs text-muted-foreground">Connected via Catalyst key</p>
                  </div>
                </div>
                <Badge variant="secondary" className="text-xs">Active</Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/10 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">LiteLLM</p>
                    <p className="text-xs text-muted-foreground">Advanced routing enabled</p>
                  </div>
                </div>
                <Badge variant="secondary" className="text-xs">Active</Badge>
              </div>
            </div>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};

export default SettingsPanel;
