{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        catalyst: \"catalyst-gradient-bg text-white shadow-lg catalyst-glow-hover\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\nimport { Settings, Save, Download, Upload } from \"lucide-react\"\n\nexport function Header() {\n  return (\n    <header className=\"border-b border-border bg-card/50 backdrop-blur-sm\">\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Logo and Title */}\n        <div className=\"flex items-center gap-3\">\n          <Image\n            src=\"/catalyst-logo.png\"\n            alt=\"Catalyst\"\n            width={32}\n            height={32}\n            className=\"catalyst-glow\"\n          />\n          <div>\n            <h1 className=\"text-xl font-bold catalyst-gradient-text\">\n              Catalyst\n            </h1>\n            <p className=\"text-xs text-muted-foreground\">\n              Prompt Engineering Ecosystem\n            </p>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"ghost\" size=\"sm\">\n            <Upload className=\"h-4 w-4\" />\n            Import\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Download className=\"h-4 w-4\" />\n            Export\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Save className=\"h-4 w-4\" />\n            Save\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Settings className=\"h-4 w-4\" />\n            Settings\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGhC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Button } from \"@/components/ui/button\"\nimport { \n  FileText, \n  Zap, \n  MessageSquare, \n  Bug, \n  Brain, \n  ChevronDown,\n  Plus\n} from \"lucide-react\"\n\nconst moduleCategories = [\n  {\n    name: \"Core Modules\",\n    icon: FileText,\n    modules: [\n      { name: \"Text Generator\", description: \"Generate text content\" },\n      { name: \"Code Assistant\", description: \"Help with coding tasks\" },\n      { name: \"Data Analyzer\", description: \"Analyze and interpret data\" },\n    ]\n  },\n  {\n    name: \"Prompt Engines\",\n    icon: Zap,\n    modules: [\n      { name: \"Writer Engine\", description: \"Creative writing assistant\" },\n      { name: \"Interview Engine\", description: \"Conduct structured interviews\" },\n      { name: \"Debug Engine\", description: \"Debug and troubleshoot\" },\n    ]\n  },\n  {\n    name: \"Specialized\",\n    icon: Brain,\n    modules: [\n      { name: \"Chain of Draft\", description: \"Iterative content refinement\" },\n      { name: \"Prompt4Me\", description: \"Intelligent prompt suggestions\" },\n    ]\n  }\n]\n\nexport function Sidebar() {\n  const [expandedCategories, setExpandedCategories] = useState<string[]>([\n    \"Core Modules\"\n  ])\n\n  const toggleCategory = (categoryName: string) => {\n    setExpandedCategories(prev =>\n      prev.includes(categoryName)\n        ? prev.filter(name => name !== categoryName)\n        : [...prev, categoryName]\n    )\n  }\n\n  return (\n    <aside className=\"w-80 border-r border-border bg-card/30 backdrop-blur-sm\">\n      <div className=\"p-4\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold\">Module Library</h2>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Plus className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        <div className=\"space-y-2\">\n          {moduleCategories.map((category) => {\n            const Icon = category.icon\n            const isExpanded = expandedCategories.includes(category.name)\n\n            return (\n              <div key={category.name} className=\"space-y-1\">\n                <Button\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => toggleCategory(category.name)}\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {category.name}\n                  <ChevronDown \n                    className={`h-4 w-4 ml-auto transition-transform ${\n                      isExpanded ? \"rotate-180\" : \"\"\n                    }`} \n                  />\n                </Button>\n\n                {isExpanded && (\n                  <div className=\"ml-6 space-y-1\">\n                    {category.modules.map((module) => (\n                      <div\n                        key={module.name}\n                        className=\"p-3 rounded-md border border-border/50 bg-background/50 hover:bg-accent/50 cursor-pointer transition-colors\"\n                        draggable\n                        onDragStart={(e) => {\n                          e.dataTransfer.setData(\"text/plain\", JSON.stringify(module))\n                        }}\n                      >\n                        <div className=\"text-sm font-medium\">{module.name}</div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {module.description}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    </aside>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAcA,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,MAAM,8MAAA,CAAA,WAAQ;QACd,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;YAAwB;YAC/D;gBAAE,MAAM;gBAAkB,aAAa;YAAyB;YAChE;gBAAE,MAAM;gBAAiB,aAAa;YAA6B;SACpE;IACH;IACA;QACE,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;QACT,SAAS;YACP;gBAAE,MAAM;gBAAiB,aAAa;YAA6B;YACnE;gBAAE,MAAM;gBAAoB,aAAa;YAAgC;YACzE;gBAAE,MAAM;gBAAgB,aAAa;YAAyB;SAC/D;IACH;IACA;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;YAA+B;YACtE;gBAAE,MAAM;gBAAa,aAAa;YAAiC;SACpE;IACH;CACD;AAEM,SAAS;IACd,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACrE;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,gBACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,gBAC7B;mBAAI;gBAAM;aAAa;IAE/B;IAEA,qBACE,8OAAC;QAAM,WAAU;kBACf,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;sCAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIpB,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC;wBACrB,MAAM,OAAO,SAAS,IAAI;wBAC1B,MAAM,aAAa,mBAAmB,QAAQ,CAAC,SAAS,IAAI;wBAE5D,qBACE,8OAAC;4BAAwB,WAAU;;8CACjC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,eAAe,SAAS,IAAI;;sDAE3C,8OAAC;4CAAK,WAAU;;;;;;wCACf,SAAS,IAAI;sDACd,8OAAC,oNAAA,CAAA,cAAW;4CACV,WAAW,CAAC,qCAAqC,EAC/C,aAAa,eAAe,IAC5B;;;;;;;;;;;;gCAIL,4BACC,8OAAC;oCAAI,WAAU;8CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC;4CAEC,WAAU;4CACV,SAAS;4CACT,aAAa,CAAC;gDACZ,EAAE,YAAY,CAAC,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC;4CACtD;;8DAEA,8OAAC;oDAAI,WAAU;8DAAuB,OAAO,IAAI;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DACZ,OAAO,WAAW;;;;;;;2CAThB,OAAO,IAAI;;;;;;;;;;;2BAnBhB,SAAS,IAAI;;;;;oBAoC3B;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/workspace/workspace-module.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSortable } from \"@dnd-kit/sortable\"\nimport { CSS } from \"@dnd-kit/utilities\"\nimport { Button } from \"@/components/ui/button\"\nimport { GripVertical, X, Settings, Play } from \"lucide-react\"\n\ninterface Module {\n  id: string\n  name: string\n  description: string\n  type: string\n}\n\ninterface WorkspaceModuleProps {\n  module: Module\n  index: number\n  onRemove: (moduleId: string) => void\n}\n\nexport function WorkspaceModule({ module, index, onRemove }: WorkspaceModuleProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: module.id })\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  }\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      className={`group relative bg-card border border-border rounded-lg p-4 ${\n        isDragging ? \"opacity-50\" : \"\"\n      }`}\n    >\n      {/* Module Header */}\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center gap-3\">\n          <div\n            {...attributes}\n            {...listeners}\n            className=\"cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded\"\n          >\n            <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\n          </div>\n          <div>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-xs bg-primary/20 text-primary px-2 py-1 rounded\">\n                #{index + 1}\n              </span>\n              <h3 className=\"font-medium\">{module.name}</h3>\n            </div>\n            <p className=\"text-sm text-muted-foreground mt-1\">\n              {module.description}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <Button variant=\"ghost\" size=\"sm\">\n            <Settings className=\"h-4 w-4\" />\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Play className=\"h-4 w-4\" />\n          </Button>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\"\n            onClick={() => onRemove(module.id)}\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Module Content */}\n      <div className=\"space-y-3\">\n        {/* Input Section */}\n        <div>\n          <label className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n            Input\n          </label>\n          <div className=\"mt-1 p-3 bg-muted/50 rounded border border-dashed border-border\">\n            <textarea\n              placeholder=\"Enter your prompt or connect from previous module...\"\n              className=\"w-full bg-transparent border-none outline-none resize-none text-sm\"\n              rows={2}\n            />\n          </div>\n        </div>\n\n        {/* Output Section */}\n        <div>\n          <label className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n            Output\n          </label>\n          <div className=\"mt-1 p-3 bg-background/50 rounded border border-border min-h-[60px]\">\n            <div className=\"text-sm text-muted-foreground italic\">\n              Output will appear here after execution...\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Connection Indicator */}\n      {index > 0 && (\n        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n          <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n          <div className=\"w-px h-4 bg-border mx-auto\"></div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAoBO,SAAS,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAwB;IAC/E,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,OAAO,EAAE;IAAC;IAEhC,MAAM,QAAQ;QACZ,WAAW,qKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAC,2DAA2D,EACrE,aAAa,eAAe,IAC5B;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACE,GAAG,UAAU;gCACb,GAAG,SAAS;gCACb,WAAU;0CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAE1B,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAAuD;oDACnE,QAAQ;;;;;;;0DAEZ,8OAAC;gDAAG,WAAU;0DAAe,OAAO,IAAI;;;;;;;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,OAAO,EAAE;0CAEjC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAoE;;;;;;0CAGrF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,aAAY;oCACZ,WAAU;oCACV,MAAM;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAoE;;;;;;0CAGrF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;YAQ3D,QAAQ,mBACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/workspace/workspace.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  closestCenter,\n} from \"@dnd-kit/core\"\nimport {\n  SortableContext,\n  verticalListSortingStrategy,\n} from \"@dnd-kit/sortable\"\nimport { WorkspaceModule } from \"./workspace-module\"\nimport { Button } from \"@/components/ui/button\"\nimport { Play, Plus } from \"lucide-react\"\n\ninterface Module {\n  id: string\n  name: string\n  description: string\n  type: string\n}\n\nexport function Workspace() {\n  const [modules, setModules] = useState<Module[]>([])\n  const [activeModule, setActiveModule] = useState<Module | null>(null)\n\n  const handleDragStart = (event: DragStartEvent) => {\n    const module = modules.find(m => m.id === event.active.id)\n    setActiveModule(module || null)\n  }\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    setActiveModule(null)\n    \n    const { active, over } = event\n    \n    if (!over) return\n\n    // Handle dropping from sidebar\n    if (typeof active.id === \"string\" && active.id.startsWith(\"sidebar-\")) {\n      try {\n        const moduleData = JSON.parse(active.data.current?.moduleData || \"{}\")\n        const newModule: Module = {\n          id: `module-${Date.now()}`,\n          name: moduleData.name || \"New Module\",\n          description: moduleData.description || \"\",\n          type: \"custom\"\n        }\n        setModules(prev => [...prev, newModule])\n      } catch (error) {\n        console.error(\"Error parsing module data:\", error)\n      }\n      return\n    }\n\n    // Handle reordering existing modules\n    if (active.id !== over.id) {\n      setModules((items) => {\n        const oldIndex = items.findIndex(item => item.id === active.id)\n        const newIndex = items.findIndex(item => item.id === over.id)\n        \n        const newItems = [...items]\n        const [reorderedItem] = newItems.splice(oldIndex, 1)\n        newItems.splice(newIndex, 0, reorderedItem)\n        \n        return newItems\n      })\n    }\n  }\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault()\n    \n    try {\n      const moduleData = JSON.parse(event.dataTransfer.getData(\"text/plain\"))\n      const newModule: Module = {\n        id: `module-${Date.now()}`,\n        name: moduleData.name,\n        description: moduleData.description,\n        type: \"library\"\n      }\n      setModules(prev => [...prev, newModule])\n    } catch (error) {\n      console.error(\"Error handling drop:\", error)\n    }\n  }\n\n  const removeModule = (moduleId: string) => {\n    setModules(prev => prev.filter(m => m.id !== moduleId))\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col\">\n      {/* Workspace Header */}\n      <div className=\"border-b border-border bg-card/30 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-lg font-semibold\">Prompt Chain Workspace</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Drag modules here to build your prompt chain\n            </p>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Module\n            </Button>\n            <Button variant=\"catalyst\" size=\"sm\" disabled={modules.length === 0}>\n              <Play className=\"h-4 w-4 mr-2\" />\n              Run Chain\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Drop Zone */}\n      <div\n        className=\"flex-1 p-6\"\n        onDrop={handleDrop}\n        onDragOver={(e) => e.preventDefault()}\n      >\n        <DndContext\n          collisionDetection={closestCenter}\n          onDragStart={handleDragStart}\n          onDragEnd={handleDragEnd}\n        >\n          <SortableContext items={modules} strategy={verticalListSortingStrategy}>\n            {modules.length === 0 ? (\n              <div className=\"flex-1 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"w-24 h-24 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center\">\n                    <Plus className=\"h-8 w-8 text-muted-foreground\" />\n                  </div>\n                  <h3 className=\"text-lg font-medium mb-2\">Empty Workspace</h3>\n                  <p className=\"text-muted-foreground max-w-md\">\n                    Drag modules from the sidebar to start building your prompt chain.\n                    Connect modules to create powerful AI workflows.\n                  </p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {modules.map((module, index) => (\n                  <WorkspaceModule\n                    key={module.id}\n                    module={module}\n                    index={index}\n                    onRemove={removeModule}\n                  />\n                ))}\n              </div>\n            )}\n          </SortableContext>\n\n          <DragOverlay>\n            {activeModule ? (\n              <div className=\"p-4 bg-card border border-border rounded-lg shadow-lg\">\n                <div className=\"font-medium\">{activeModule.name}</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {activeModule.description}\n                </div>\n              </div>\n            ) : null}\n          </DragOverlay>\n        </DndContext>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAIA;AACA;AACA;AAAA;AAhBA;;;;;;;;AAyBO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE;QACzD,gBAAgB,UAAU;IAC5B;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAEhB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,MAAM;QAEX,+BAA+B;QAC/B,IAAI,OAAO,OAAO,EAAE,KAAK,YAAY,OAAO,EAAE,CAAC,UAAU,CAAC,aAAa;YACrE,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,cAAc;gBACjE,MAAM,YAAoB;oBACxB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;oBAC1B,MAAM,WAAW,IAAI,IAAI;oBACzB,aAAa,WAAW,WAAW,IAAI;oBACvC,MAAM;gBACR;gBACA,WAAW,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YACzC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;YACA;QACF;QAEA,qCAAqC;QACrC,IAAI,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YACzB,WAAW,CAAC;gBACV,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;gBAC9D,MAAM,WAAW,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,EAAE;gBAE5D,MAAM,WAAW;uBAAI;iBAAM;gBAC3B,MAAM,CAAC,cAAc,GAAG,SAAS,MAAM,CAAC,UAAU;gBAClD,SAAS,MAAM,CAAC,UAAU,GAAG;gBAE7B,OAAO;YACT;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QAEpB,IAAI;YACF,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,YAAY,CAAC,OAAO,CAAC;YACzD,MAAM,YAAoB;gBACxB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,MAAM;YACR;YACA,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAW,MAAK;oCAAK,UAAU,QAAQ,MAAM,KAAK;;sDAChE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBACC,WAAU;gBACV,QAAQ;gBACR,YAAY,CAAC,IAAM,EAAE,cAAc;0BAEnC,cAAA,8OAAC,2JAAA,CAAA,aAAU;oBACT,oBAAoB,2JAAA,CAAA,gBAAa;oBACjC,aAAa;oBACb,WAAW;;sCAEX,8OAAC,mKAAA,CAAA,kBAAe;4BAAC,OAAO;4BAAS,UAAU,mKAAA,CAAA,8BAA2B;sCACnE,QAAQ,MAAM,KAAK,kBAClB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAAiC;;;;;;;;;;;;;;;;qDAOlD,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,sJAAA,CAAA,kBAAe;wCAEd,QAAQ;wCACR,OAAO;wCACP,UAAU;uCAHL,OAAO,EAAE;;;;;;;;;;;;;;;sCAUxB,8OAAC,2JAAA,CAAA,cAAW;sCACT,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAe,aAAa,IAAI;;;;;;kDAC/C,8OAAC;wCAAI,WAAU;kDACZ,aAAa,WAAW;;;;;;;;;;;uCAG3B;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}