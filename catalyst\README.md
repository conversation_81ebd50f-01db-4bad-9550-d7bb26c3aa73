# Catalyst 🚀

**A comprehensive prompt engineering ecosystem and SaaS platform for creating, managing, and executing .promptx modules with advanced AI model integration.**

![Catalyst](https://img.shields.io/badge/Status-Active%20Development-brightgreen)
![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.0-38bdf8)

## 🌟 Overview

Catalyst is a next-generation prompt engineering platform that revolutionizes how developers and content creators work with AI models. Built with modern web technologies, it provides a comprehensive ecosystem for creating, managing, and executing sophisticated prompt modules through an intuitive, professional interface.

### Key Features

- **🔧 .promptx Module System**: Custom JSON-based format for structured prompt engineering
- **🎨 Professional UI/UX**: Catalyst-branded interface with enhanced user experience
- **⚡ Real-time Preview**: Live prompt generation with dynamic variable substitution
- **📚 Module Library**: Organized collection with search, filtering, and categorization
- **🔄 Import/Export**: Full file system integration for sharing and backup
- **✅ Validation System**: Comprehensive error checking and schema validation
- **🎯 Template System**: Pre-built patterns for common use cases
- **🔗 Multi-Model Support**: Integration with GPT-4o, Claude 3.5 Sonnet, Gemini, and more

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/MetamorphicLabsAI/Catalyst.git
   cd Catalyst
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

### Build for Production

```bash
npm run build
npm start
```

## 📖 Usage

### Creating Your First Module

1. **Navigate to the Workspace** → **Create** tab
2. **Choose a template** or start with a custom module
3. **Define your prompt** with variables using `{{variableName}}` syntax
4. **Configure metadata** including name, description, tags, and category
5. **Test with preview** using the real-time variable substitution
6. **Save and execute** your module

### Working with Variables

Catalyst supports multiple variable types:

- **Text**: Free-form text input
- **Number**: Numeric values with validation
- **Boolean**: True/false checkboxes
- **Select**: Single-choice dropdown
- **Multiselect**: Multiple-choice options

### Module Categories

- **Core**: Essential prompt patterns for common tasks
- **Engine**: Complex workflow automation chains
- **Specialized**: Domain-specific prompt modules
- **Custom**: User-created modules
- **Template**: Reusable patterns and starting points

## 🎯 Sample Modules

Catalyst includes professional sample modules:

- **Content Writer**: Marketing copy, blog posts, articles
- **Code Analyzer**: Bug detection, performance optimization
- **Meeting Summarizer**: Structured meeting notes with action items
- **Research Assistant**: Comprehensive analysis and insights
- **Email Composer**: Professional business communication

## 🔧 .promptx File Format

The `.promptx` format is a JSON-based structure for prompt modules:

```json
{
  "schema": "1.0.0",
  "metadata": {
    "name": "Content Writer",
    "description": "Professional content writing assistant",
    "version": "1.2.0",
    "category": "core",
    "tags": ["writing", "content", "marketing"],
    "formattingStyle": "markdown",
    "type": "single"
  },
  "content": {
    "systemPrompt": "You are an expert content writer...",
    "userPrompt": "Write a {{content_type}} about {{topic}}...",
    "variables": [
      {
        "name": "content_type",
        "type": "select",
        "options": ["blog post", "article", "social media post"],
        "required": true
      }
    ]
  }
}
```

## 🛠️ Development

### Project Structure

```
catalyst/
├── src/
│   ├── app/                 # Next.js app router
│   ├── components/          # React components
│   │   ├── modules/         # Module management components
│   │   ├── workspace/       # Workspace interface
│   │   ├── ui/             # Reusable UI components
│   │   └── layout/         # Layout components
│   ├── lib/                # Utility functions
│   ├── types/              # TypeScript definitions
│   └── data/               # Sample data and modules
├── public/                 # Static assets
└── package.json           # Dependencies and scripts
```

### Key Scripts

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run start`: Start production server
- `npm run lint`: Run ESLint

## 📄 License

This project is licensed under the MIT License.

---

**Catalyst** - Empowering the future of prompt engineering 🚀
