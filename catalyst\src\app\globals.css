@import "tailwindcss";

/* Catalyst Custom Theme */
:root {
  /* Catalyst Brand Colors */
  --catalyst-primary: #6366f1;
  --catalyst-primary-dark: #4f46e5;
  --catalyst-secondary: #8b5cf6;
  --catalyst-accent: #06b6d4;
  --catalyst-glow: #a855f7;

  /* Gradients */
  --catalyst-gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --catalyst-gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --catalyst-gradient-glow: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);

  /* Dark theme base */
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1a1a1a;
  --card-foreground: #ededed;
  --popover: #1a1a1a;
  --popover-foreground: #ededed;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #262626;
  --input: #262626;
  --ring: #6366f1;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Catalyst Glow Effects */
.catalyst-glow {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

.catalyst-glow-hover:hover {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
  transition: box-shadow 0.3s ease;
}

/* Catalyst Gradients */
.catalyst-gradient-bg {
  background: var(--catalyst-gradient-primary);
}

.catalyst-gradient-text {
  background: var(--catalyst-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-foreground);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  min-height: 100vh;
}

/* Enhanced Catalyst Styling */
.catalyst-border {
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.catalyst-border:hover {
  border-color: rgba(139, 92, 246, 0.4);
}

/* Enhanced Module Cards */
.module-card {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.module-card:hover {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

/* Prompt Panel Styling */
.prompt-panel {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  backdrop-filter: blur(12px);
}

/* Enhanced Scrollbars */
.catalyst-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.catalyst-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

.catalyst-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--catalyst-primary), var(--catalyst-accent));
  border-radius: 4px;
}

.catalyst-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(20, 184, 166, 0.8));
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Gradient text utility */
.gradient-text {
  background: var(--catalyst-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Catalyst color utilities */
.text-catalyst-purple {
  color: #8b5cf6;
}

.text-catalyst-teal {
  color: #14b8a6;
}

.bg-catalyst-dark {
  background-color: #0f172a;
}
