
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import CatalystHeader from "@/components/CatalystHeader";
import ModuleSidebar from "@/components/ModuleSidebar";
import PromptWorkspace from "@/components/PromptWorkspace";
import OutputPanel from "@/components/OutputPanel";
import SettingsPanel from "@/components/SettingsPanel";
import { ArrowRight, Zap, Link, Brain, Settings } from "lucide-react";

const Index = () => {
  const [activeTab, setActiveTab] = useState("workspace");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className="min-h-screen bg-catalyst-dark text-foreground">
      <CatalystHeader />
      
      <div className="flex h-[calc(100vh-64px)]">
        {/* Module Library Sidebar */}
        <ModuleSidebar 
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="border-b border-border px-4 py-2">
              <TabsList className="bg-muted/20">
                <TabsTrigger value="workspace" className="flex items-center gap-2">
                  <Link className="w-4 h-4" />
                  Workspace
                </TabsTrigger>
                <TabsTrigger value="output" className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Output
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="workspace" className="h-full m-0">
                <PromptWorkspace />
              </TabsContent>
              
              <TabsContent value="output" className="h-full m-0">
                <OutputPanel />
              </TabsContent>
              
              <TabsContent value="settings" className="h-full m-0">
                <SettingsPanel />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Index;
