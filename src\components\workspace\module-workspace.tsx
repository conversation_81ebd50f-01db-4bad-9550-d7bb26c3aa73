"use client"

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Plus, 
  Upload, 
  Settings, 
  Play,
  Edit,
  Library,
  Zap
} from 'lucide-react';
import { ModuleManager } from '@/components/modules/module-manager';
import { ModuleEditor } from '@/components/modules/module-editor';
import { PromptxModule } from '@/types/promptx';
import { SAMPLE_MODULES } from '@/data/sample-modules';
import { PromptxUtils } from '@/lib/promptx';

export function ModuleWorkspace() {
  const [modules, setModules] = useState<PromptxModule[]>(SAMPLE_MODULES);
  const [selectedModule, setSelectedModule] = useState<PromptxModule | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('library');

  const handleModuleLoad = (module: PromptxModule) => {
    // Check if module already exists (by name and creation date)
    const existingIndex = modules.findIndex(m => 
      m.metadata.name === module.metadata.name && 
      m.metadata.createdAt === module.metadata.createdAt
    );
    
    if (existingIndex >= 0) {
      // Update existing module
      const updatedModules = [...modules];
      updatedModules[existingIndex] = module;
      setModules(updatedModules);
    } else {
      // Add new module
      setModules(prev => [...prev, module]);
    }
  };

  const handleModuleCreate = (template: string) => {
    let newModule: PromptxModule;
    
    switch (template) {
      case 'writer':
        newModule = PromptxUtils.createFromTemplate('New Writer Module', 'writer');
        break;
      case 'analyzer':
        newModule = PromptxUtils.createFromTemplate('New Analyzer Module', 'analyzer');
        break;
      case 'coder':
        newModule = PromptxUtils.createFromTemplate('New Coder Module', 'coder');
        break;
      case 'qa':
        newModule = PromptxUtils.createFromTemplate('New Q&A Module', 'qa');
        break;
      default:
        newModule = PromptxUtils.createEmpty();
        break;
    }
    
    setSelectedModule(newModule);
    setIsEditorOpen(true);
  };

  const handleModuleEdit = (module: PromptxModule) => {
    setSelectedModule(module);
    setIsEditorOpen(true);
  };

  const handleModuleDelete = (moduleId: string) => {
    setModules(prev => prev.filter(m => `${m.metadata.name}-${m.metadata.createdAt}` !== moduleId));
  };

  const handleModuleExecute = (module: PromptxModule) => {
    // For now, just open the editor in preview mode
    setSelectedModule(module);
    setIsEditorOpen(true);
  };

  const handleModuleSave = (module: PromptxModule) => {
    handleModuleLoad(module);
  };

  const getQuickStartModules = () => {
    return modules.filter(m => m.metadata.category === 'core').slice(0, 4);
  };

  const getEngineModules = () => {
    return modules.filter(m => m.metadata.category === 'engine');
  };

  const getRecentModules = () => {
    return [...modules]
      .sort((a, b) => new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime())
      .slice(0, 6);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="library">
              <Library className="w-4 h-4 mr-2" />
              Module Library
            </TabsTrigger>
            <TabsTrigger value="engines">
              <Zap className="w-4 h-4 mr-2" />
              Prompt Engines
            </TabsTrigger>
            <TabsTrigger value="recent">
              <FileText className="w-4 h-4 mr-2" />
              Recent
            </TabsTrigger>
            <TabsTrigger value="create">
              <Plus className="w-4 h-4 mr-2" />
              Create
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-auto p-6">
            <TabsContent value="library" className="mt-0 h-full">
              <ModuleManager
                modules={modules}
                onModuleLoad={handleModuleLoad}
                onModuleCreate={handleModuleCreate}
                onModuleEdit={handleModuleEdit}
                onModuleDelete={handleModuleDelete}
                onModuleExecute={handleModuleExecute}
              />
            </TabsContent>

            <TabsContent value="engines" className="mt-0 space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-4">Prompt Engines</h2>
                <p className="text-muted-foreground mb-6">
                  Pre-built chains of modules for complex workflows
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getEngineModules().map((module) => (
                  <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className="module-card group">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Zap className="w-5 h-5 text-catalyst-purple" />
                          <CardTitle className="text-lg">{module.metadata.name}</CardTitle>
                        </div>
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                          Engine
                        </Badge>
                      </div>
                      <CardDescription>{module.metadata.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {module.metadata.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button size="sm" onClick={() => handleModuleExecute(module)}>
                          <Play className="w-4 h-4 mr-2" />
                          Run
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleModuleEdit(module)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {getEngineModules().length === 0 && (
                <Card className="text-center py-12">
                  <CardContent>
                    <Zap className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <CardTitle className="text-lg mb-2">No engines available</CardTitle>
                    <CardDescription className="mb-4">
                      Create your first prompt engine to automate complex workflows
                    </CardDescription>
                    <Button onClick={() => handleModuleCreate('custom')}>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Engine
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="recent" className="mt-0 space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-4">Recent Modules</h2>
                <p className="text-muted-foreground mb-6">
                  Recently created or modified modules
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getRecentModules().map((module) => (
                  <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className="module-card group">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{module.metadata.name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {new Date(module.metadata.updatedAt).toLocaleDateString()}
                        </Badge>
                      </div>
                      <CardDescription>{module.metadata.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {module.metadata.tags.slice(0, 3).map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button size="sm" onClick={() => handleModuleExecute(module)}>
                          <Play className="w-4 h-4 mr-2" />
                          Use
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleModuleEdit(module)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="create" className="mt-0 space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-4">Create New Module</h2>
                <p className="text-muted-foreground mb-6">
                  Start with a template or create from scratch
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="cursor-pointer hover:shadow-lg transition-shadow catalyst-border" onClick={() => handleModuleCreate('writer')}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Content Writer
                    </CardTitle>
                    <CardDescription>
                      Create articles, blogs, and marketing copy
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="secondary" className="text-xs">writing</Badge>
                      <Badge variant="secondary" className="text-xs">content</Badge>
                      <Badge variant="secondary" className="text-xs">marketing</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg transition-shadow catalyst-border" onClick={() => handleModuleCreate('analyzer')}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Content Analyzer
                    </CardTitle>
                    <CardDescription>
                      Analyze and provide insights on content
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="secondary" className="text-xs">analysis</Badge>
                      <Badge variant="secondary" className="text-xs">insights</Badge>
                      <Badge variant="secondary" className="text-xs">review</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg transition-shadow catalyst-border" onClick={() => handleModuleCreate('custom')}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Plus className="w-5 h-5" />
                      Custom Module
                    </CardTitle>
                    <CardDescription>
                      Start from scratch with a blank module
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="secondary" className="text-xs">custom</Badge>
                      <Badge variant="secondary" className="text-xs">blank</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                <div className="flex gap-4">
                  <Button variant="outline" onClick={() => document.getElementById('file-import')?.click()}>
                    <Upload className="w-4 h-4 mr-2" />
                    Import .promptx File
                  </Button>
                  <input
                    id="file-import"
                    type="file"
                    accept=".promptx,.json"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          const content = event.target?.result as string;
                          const { module } = PromptxUtils.parse(content);
                          if (module) {
                            handleModuleLoad(module);
                            setActiveTab('library');
                          }
                        };
                        reader.readAsText(file);
                      }
                    }}
                  />
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      <ModuleEditor
        module={selectedModule}
        isOpen={isEditorOpen}
        onClose={() => {
          setIsEditorOpen(false);
          setSelectedModule(null);
        }}
        onSave={handleModuleSave}
        onExecute={handleModuleExecute}
      />
    </div>
  );
}
