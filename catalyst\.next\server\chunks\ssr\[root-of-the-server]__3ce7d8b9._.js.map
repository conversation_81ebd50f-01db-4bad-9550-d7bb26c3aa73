{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        catalyst: \"catalyst-gradient-bg text-white shadow-lg catalyst-glow-hover\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\nimport { Settings, Save, Download, Upload } from \"lucide-react\"\n\nexport function Header() {\n  return (\n    <header className=\"border-b border-border bg-card/50 backdrop-blur-sm\">\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Logo and Title */}\n        <div className=\"flex items-center gap-3\">\n          <Image\n            src=\"/catalyst-logo.png\"\n            alt=\"Catalyst\"\n            width={32}\n            height={32}\n            className=\"catalyst-glow\"\n          />\n          <div>\n            <h1 className=\"text-xl font-bold catalyst-gradient-text\">\n              Catalyst\n            </h1>\n            <p className=\"text-xs text-muted-foreground\">\n              Prompt Engineering Ecosystem\n            </p>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"ghost\" size=\"sm\">\n            <Upload className=\"h-4 w-4\" />\n            Import\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Download className=\"h-4 w-4\" />\n            Export\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Save className=\"h-4 w-4\" />\n            Save\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            <Settings className=\"h-4 w-4\" />\n            Settings\n          </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGhC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC3B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport {\n  ChevronDown,\n  ChevronRight,\n  ChevronLeft,\n  FileText,\n  MessageSquare,\n  Code,\n  Palette,\n  Brain,\n  Zap,\n  Search,\n  Plus,\n  Download\n} from \"lucide-react\"\n\ninterface SidebarProps {\n  collapsed: boolean;\n  onToggleCollapse: () => void;\n}\n\nconst moduleCategories = [\n  {\n    name: \"Core Modules\",\n    icon: Brain,\n    modules: [\n      { name: \"System Context\", description: \"Define AI behavior and role\", icon: MessageSquare, tags: [\"system\", \"context\"] },\n      { name: \"User Input\", description: \"Capture user requirements\", icon: FileText, tags: [\"input\", \"user\"] },\n      { name: \"Output Format\", description: \"Structure response format\", icon: Code, tags: [\"output\", \"format\"] },\n    ]\n  },\n  {\n    name: \"Prompt Engines\",\n    icon: Zap,\n    modules: [\n      { name: \"Writer Engine\", description: \"Outline → Expand → Style\", icon: Palette, tags: [\"writing\", \"engine\"] },\n      { name: \"Interview Bot\", description: \"Role-play Q&A trainer\", icon: MessageSquare, tags: [\"interview\", \"qa\"] },\n      { name: \"Debug Console\", description: \"Analyze and fix code\", icon: Code, tags: [\"debug\", \"code\"] },\n    ]\n  },\n  {\n    name: \"Specialized\",\n    icon: Palette,\n    modules: [\n      { name: \"Chain of Draft\", description: \"Multi-stage refinement\", icon: FileText, tags: [\"chain\", \"draft\"] },\n      { name: \"Model Adapter\", description: \"Optimize for target model\", icon: Brain, tags: [\"adapter\", \"model\"] },\n      { name: \"Memory Context\", description: \"Session-based memory\", icon: Zap, tags: [\"memory\", \"context\"] },\n    ]\n  }\n]\n\nconst savedModules = [\n  { id: \"1\", name: \"Blog Writer\", icon: FileText, tags: [\"writing\", \"blog\"] },\n  { id: \"2\", name: \"Code Reviewer\", icon: Code, tags: [\"code\", \"review\"] },\n  { id: \"3\", name: \"Research Assistant\", icon: Brain, tags: [\"research\", \"analysis\"] },\n]\n\nexport function Sidebar({ collapsed, onToggleCollapse }: SidebarProps) {\n  const [expandedCategories, setExpandedCategories] = useState<string[]>([\"Core Modules\"])\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  const toggleCategory = (categoryName: string) => {\n    setExpandedCategories(prev =>\n      prev.includes(categoryName)\n        ? prev.filter(name => name !== categoryName)\n        : [...prev, categoryName]\n    )\n  }\n\n  return (\n    <div className={`${collapsed ? 'w-16' : 'w-80'} border-r border-border bg-card/20 backdrop-blur-sm transition-all duration-300 flex flex-col`}>\n      {/* Sidebar Header */}\n      <div className=\"p-4 border-b border-border flex items-center justify-between\">\n        {!collapsed && (\n          <div>\n            <h2 className=\"font-semibold text-sm\">Module Library</h2>\n            <p className=\"text-xs text-muted-foreground\">Engines & Modules</p>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={onToggleCollapse}\n          className=\"p-2\"\n        >\n          {collapsed ? <ChevronRight className=\"w-4 h-4\" /> : <ChevronLeft className=\"w-4 h-4\" />}\n        </Button>\n      </div>\n\n      <ScrollArea className=\"flex-1\">\n        <div className=\"p-4 space-y-6\">\n          {/* Search */}\n          {!collapsed && (\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search modules...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 catalyst-border\"\n              />\n            </div>\n          )}\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            {!collapsed && <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Quick Start</h3>}\n\n            <Button\n              variant=\"outline\"\n              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}\n            >\n              <Brain className=\"w-4 h-4 text-catalyst-purple\" />\n              {!collapsed && \"Prompt4Me Assistant\"}\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              className={`${collapsed ? 'w-full p-2' : 'w-full justify-start gap-2'} catalyst-border hover:catalyst-glow`}\n            >\n              <Palette className=\"w-4 h-4 text-catalyst-teal\" />\n              {!collapsed && \"PromptForge Lite\"}\n            </Button>\n          </div>\n\n          {/* Module Categories */}\n          <div className=\"space-y-4\">\n            {!collapsed && <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Module Categories</h3>}\n\n            {moduleCategories.map((category) => {\n              const isExpanded = expandedCategories.includes(category.name)\n              const Icon = category.icon\n\n              return (\n                <Card key={category.name} className=\"catalyst-border\">\n                  <CardHeader\n                    className=\"pb-2 cursor-pointer\"\n                    onClick={() => !collapsed && toggleCategory(category.name)}\n                  >\n                    <CardTitle className=\"flex items-center justify-between text-sm\">\n                      <div className=\"flex items-center gap-2\">\n                        <Icon className=\"w-4 h-4 text-catalyst-purple\" />\n                        {!collapsed && category.name}\n                      </div>\n                      {!collapsed && (isExpanded ? (\n                        <ChevronDown className=\"w-4 h-4\" />\n                      ) : (\n                        <ChevronRight className=\"w-4 h-4\" />\n                      ))}\n                    </CardTitle>\n                  </CardHeader>\n\n                  {!collapsed && isExpanded && (\n                    <CardContent className=\"pt-0 space-y-2\">\n                      {category.modules.map((module) => {\n                        const ModuleIcon = module.icon\n                        return (\n                          <Card key={module.name} className=\"module-card cursor-pointer p-3\">\n                            <div className=\"flex items-start gap-3\">\n                              <ModuleIcon className=\"w-4 h-4 text-catalyst-purple mt-0.5\" />\n                              <div className=\"flex-1 min-w-0\">\n                                <h4 className=\"font-medium text-sm mb-1\">{module.name}</h4>\n                                <p className=\"text-xs text-muted-foreground mb-2\">{module.description}</p>\n                                <div className=\"flex flex-wrap gap-1\">\n                                  {module.tags.map((tag) => (\n                                    <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                                      {tag}\n                                    </Badge>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        )\n                      })}\n                    </CardContent>\n                  )}\n                </Card>\n              )\n            })}\n          </div>\n\n          {/* Saved Modules */}\n          {!collapsed && (\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">Saved Modules</h3>\n                <Button variant=\"ghost\" size=\"sm\" className=\"p-1\">\n                  <Plus className=\"w-3 h-3\" />\n                </Button>\n              </div>\n\n              {savedModules.map((module) => (\n                <Card key={module.id} className=\"module-card cursor-pointer p-3\">\n                  <div className=\"flex items-start gap-3\">\n                    <module.icon className=\"w-4 h-4 text-catalyst-purple mt-0.5\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-sm mb-1\">{module.name}</h4>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {module.tags.map((tag) => (\n                          <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AA4BA,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;gBAA+B,MAAM,wNAAA,CAAA,gBAAa;gBAAE,MAAM;oBAAC;oBAAU;iBAAU;YAAC;YACvH;gBAAE,MAAM;gBAAc,aAAa;gBAA6B,MAAM,8MAAA,CAAA,WAAQ;gBAAE,MAAM;oBAAC;oBAAS;iBAAO;YAAC;YACxG;gBAAE,MAAM;gBAAiB,aAAa;gBAA6B,MAAM,kMAAA,CAAA,OAAI;gBAAE,MAAM;oBAAC;oBAAU;iBAAS;YAAC;SAC3G;IACH;IACA;QACE,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;QACT,SAAS;YACP;gBAAE,MAAM;gBAAiB,aAAa;gBAA4B,MAAM,wMAAA,CAAA,UAAO;gBAAE,MAAM;oBAAC;oBAAW;iBAAS;YAAC;YAC7G;gBAAE,MAAM;gBAAiB,aAAa;gBAAyB,MAAM,wNAAA,CAAA,gBAAa;gBAAE,MAAM;oBAAC;oBAAa;iBAAK;YAAC;YAC9G;gBAAE,MAAM;gBAAiB,aAAa;gBAAwB,MAAM,kMAAA,CAAA,OAAI;gBAAE,MAAM;oBAAC;oBAAS;iBAAO;YAAC;SACnG;IACH;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,SAAS;YACP;gBAAE,MAAM;gBAAkB,aAAa;gBAA0B,MAAM,8MAAA,CAAA,WAAQ;gBAAE,MAAM;oBAAC;oBAAS;iBAAQ;YAAC;YAC1G;gBAAE,MAAM;gBAAiB,aAAa;gBAA6B,MAAM,oMAAA,CAAA,QAAK;gBAAE,MAAM;oBAAC;oBAAW;iBAAQ;YAAC;YAC3G;gBAAE,MAAM;gBAAkB,aAAa;gBAAwB,MAAM,gMAAA,CAAA,MAAG;gBAAE,MAAM;oBAAC;oBAAU;iBAAU;YAAC;SACvG;IACH;CACD;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;YAAC;YAAW;SAAO;IAAC;IAC1E;QAAE,IAAI;QAAK,MAAM;QAAiB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;YAAC;YAAQ;SAAS;IAAC;IACvE;QAAE,IAAI;QAAK,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;YAAC;YAAY;SAAW;IAAC;CACpF;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAgB;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAe;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,gBACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,gBAC7B;mBAAI;gBAAM;aAAa;IAE/B;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,YAAY,SAAS,OAAO,6FAA6F,CAAC;;0BAE3I,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAGjD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAET,0BAAY,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAAe,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI/E,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,CAAC,2BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,2BAAa,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAEjG,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,GAAG,YAAY,eAAe,6BAA6B,oCAAoC,CAAC;;sDAE3G,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,CAAC,aAAa;;;;;;;8CAGjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,GAAG,YAAY,eAAe,6BAA6B,oCAAoC,CAAC;;sDAE3G,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,CAAC,aAAa;;;;;;;;;;;;;sCAKnB,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,2BAAa,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;gCAEhG,iBAAiB,GAAG,CAAC,CAAC;oCACrB,MAAM,aAAa,mBAAmB,QAAQ,CAAC,SAAS,IAAI;oCAC5D,MAAM,OAAO,SAAS,IAAI;oCAE1B,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAAqB,WAAU;;0DAClC,8OAAC,gIAAA,CAAA,aAAU;gDACT,WAAU;gDACV,SAAS,IAAM,CAAC,aAAa,eAAe,SAAS,IAAI;0DAEzD,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;;;;;gEACf,CAAC,aAAa,SAAS,IAAI;;;;;;;wDAE7B,CAAC,aAAa,CAAC,2BACd,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;gEACzB;;;;;;;;;;;;4CAIJ,CAAC,aAAa,4BACb,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;oDACrB,MAAM,aAAa,OAAO,IAAI;oDAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;wDAAmB,WAAU;kEAChC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAW,WAAU;;;;;;8EACtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA4B,OAAO,IAAI;;;;;;sFACrD,8OAAC;4EAAE,WAAU;sFAAsC,OAAO,WAAW;;;;;;sFACrE,8OAAC;4EAAI,WAAU;sFACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,8OAAC,iIAAA,CAAA,QAAK;oFAAW,SAAQ;oFAAU,WAAU;8FAC1C;mFADS;;;;;;;;;;;;;;;;;;;;;;uDARX,OAAO,IAAI;;;;;gDAiB1B;;;;;;;uCAxCK,SAAS,IAAI;;;;;gCA6C5B;;;;;;;wBAID,CAAC,2BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAClF,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAInB,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,gIAAA,CAAA,OAAI;wCAAiB,WAAU;kDAC9B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA4B,OAAO,IAAI;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,8OAAC,iIAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAU,WAAU;8EAC1C;mEADS;;;;;;;;;;;;;;;;;;;;;;uCAPX,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBpC", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/types/promptx.ts"], "sourcesContent": ["// .promptx Module System Types and Schema\n\nexport interface PromptxVariable {\n  name: string;\n  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect';\n  description?: string;\n  required?: boolean;\n  defaultValue?: string | number | boolean;\n  options?: string[]; // For select/multiselect types\n  placeholder?: string;\n}\n\nexport interface PromptxMetadata {\n  name: string;\n  description: string;\n  version: string;\n  author?: string;\n  tags: string[];\n  category: string;\n  modelAffinity?: string[]; // Preferred models (e.g., \"gpt-4o\", \"claude-3.5-sonnet\")\n  formattingStyle: 'markdown' | 'plaintext' | 'json' | 'html';\n  type: 'single' | 'chained' | 'template';\n  uiViewPreferences?: {\n    compact?: boolean;\n    fullView?: boolean;\n    toggled?: boolean;\n  };\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PromptxContent {\n  systemPrompt?: string;\n  userPrompt: string;\n  assistantPrompt?: string; // For few-shot examples\n  variables: PromptxVariable[];\n  chainOrder?: number; // For chained modules\n  contextRetention?: boolean; // Whether to retain context for next module\n}\n\nexport interface PromptxModule {\n  metadata: PromptxMetadata;\n  content: PromptxContent;\n  schema: string; // Schema version for validation\n}\n\n// JSON Schema for .promptx validation\nexport const PROMPTX_SCHEMA = {\n  $schema: \"http://json-schema.org/draft-07/schema#\",\n  title: \"Catalyst .promptx Module\",\n  type: \"object\",\n  required: [\"metadata\", \"content\", \"schema\"],\n  properties: {\n    schema: {\n      type: \"string\",\n      const: \"1.0.0\"\n    },\n    metadata: {\n      type: \"object\",\n      required: [\"name\", \"description\", \"version\", \"tags\", \"category\", \"formattingStyle\", \"type\", \"createdAt\", \"updatedAt\"],\n      properties: {\n        name: {\n          type: \"string\",\n          minLength: 1,\n          maxLength: 100\n        },\n        description: {\n          type: \"string\",\n          minLength: 1,\n          maxLength: 500\n        },\n        version: {\n          type: \"string\",\n          pattern: \"^\\\\d+\\\\.\\\\d+\\\\.\\\\d+$\"\n        },\n        author: {\n          type: \"string\",\n          maxLength: 100\n        },\n        tags: {\n          type: \"array\",\n          items: {\n            type: \"string\",\n            minLength: 1,\n            maxLength: 50\n          },\n          maxItems: 20\n        },\n        category: {\n          type: \"string\",\n          enum: [\"core\", \"engine\", \"specialized\", \"custom\", \"template\"]\n        },\n        modelAffinity: {\n          type: \"array\",\n          items: {\n            type: \"string\",\n            enum: [\n              \"gpt-4o\", \"gpt-4o-mini\", \"gpt-4-turbo\", \"gpt-3.5-turbo\",\n              \"claude-3.5-sonnet\", \"claude-3-opus\", \"claude-3-haiku\",\n              \"gemini-pro\", \"gemini-flash\", \"llama-3.1-70b\", \"llama-3.1-8b\"\n            ]\n          }\n        },\n        formattingStyle: {\n          type: \"string\",\n          enum: [\"markdown\", \"plaintext\", \"json\", \"html\"]\n        },\n        type: {\n          type: \"string\",\n          enum: [\"single\", \"chained\", \"template\"]\n        },\n        uiViewPreferences: {\n          type: \"object\",\n          properties: {\n            compact: { type: \"boolean\" },\n            fullView: { type: \"boolean\" },\n            toggled: { type: \"boolean\" }\n          },\n          additionalProperties: false\n        },\n        createdAt: {\n          type: \"string\",\n          format: \"date-time\"\n        },\n        updatedAt: {\n          type: \"string\",\n          format: \"date-time\"\n        }\n      },\n      additionalProperties: false\n    },\n    content: {\n      type: \"object\",\n      required: [\"userPrompt\", \"variables\"],\n      properties: {\n        systemPrompt: {\n          type: \"string\",\n          maxLength: 10000\n        },\n        userPrompt: {\n          type: \"string\",\n          minLength: 1,\n          maxLength: 10000\n        },\n        assistantPrompt: {\n          type: \"string\",\n          maxLength: 10000\n        },\n        variables: {\n          type: \"array\",\n          items: {\n            type: \"object\",\n            required: [\"name\", \"type\"],\n            properties: {\n              name: {\n                type: \"string\",\n                pattern: \"^[a-zA-Z][a-zA-Z0-9_]*$\",\n                minLength: 1,\n                maxLength: 50\n              },\n              type: {\n                type: \"string\",\n                enum: [\"text\", \"number\", \"boolean\", \"select\", \"multiselect\"]\n              },\n              description: {\n                type: \"string\",\n                maxLength: 200\n              },\n              required: {\n                type: \"boolean\",\n                default: false\n              },\n              defaultValue: {\n                oneOf: [\n                  { type: \"string\" },\n                  { type: \"number\" },\n                  { type: \"boolean\" }\n                ]\n              },\n              options: {\n                type: \"array\",\n                items: {\n                  type: \"string\",\n                  minLength: 1,\n                  maxLength: 100\n                },\n                maxItems: 50\n              },\n              placeholder: {\n                type: \"string\",\n                maxLength: 100\n              }\n            },\n            additionalProperties: false\n          }\n        },\n        chainOrder: {\n          type: \"number\",\n          minimum: 0,\n          maximum: 100\n        },\n        contextRetention: {\n          type: \"boolean\",\n          default: true\n        }\n      },\n      additionalProperties: false\n    }\n  },\n  additionalProperties: false\n} as const;\n\n// Utility types for working with .promptx modules\nexport type PromptxValidationError = {\n  path: string;\n  message: string;\n  value?: any;\n};\n\nexport type PromptxValidationResult = {\n  valid: boolean;\n  errors: PromptxValidationError[];\n};\n\n// Template for creating new .promptx modules\nexport const createEmptyPromptxModule = (overrides?: Partial<PromptxModule>): PromptxModule => {\n  const now = new Date().toISOString();\n  \n  return {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"New Module\",\n      description: \"A new prompt module\",\n      version: \"1.0.0\",\n      tags: [],\n      category: \"custom\",\n      formattingStyle: \"markdown\",\n      type: \"single\",\n      createdAt: now,\n      updatedAt: now,\n      ...overrides?.metadata\n    },\n    content: {\n      userPrompt: \"Enter your prompt here...\",\n      variables: [],\n      contextRetention: true,\n      ...overrides?.content\n    },\n    ...overrides\n  };\n};\n\n// Common variable templates\nexport const COMMON_VARIABLES: Record<string, PromptxVariable> = {\n  topic: {\n    name: \"topic\",\n    type: \"text\",\n    description: \"The main topic or subject\",\n    required: true,\n    placeholder: \"Enter the topic...\"\n  },\n  tone: {\n    name: \"tone\",\n    type: \"select\",\n    description: \"The tone of voice for the output\",\n    required: false,\n    defaultValue: \"professional\",\n    options: [\"professional\", \"casual\", \"friendly\", \"formal\", \"creative\", \"technical\"]\n  },\n  audience: {\n    name: \"audience\",\n    type: \"text\",\n    description: \"Target audience for the content\",\n    required: false,\n    placeholder: \"e.g., developers, students, general public\"\n  },\n  length: {\n    name: \"length\",\n    type: \"select\",\n    description: \"Desired length of the output\",\n    required: false,\n    defaultValue: \"medium\",\n    options: [\"short\", \"medium\", \"long\", \"detailed\"]\n  },\n  format: {\n    name: \"format\",\n    type: \"select\",\n    description: \"Output format preference\",\n    required: false,\n    defaultValue: \"markdown\",\n    options: [\"markdown\", \"plaintext\", \"html\", \"json\", \"bullet-points\", \"numbered-list\"]\n  }\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;AA+CnC,MAAM,iBAAiB;IAC5B,SAAS;IACT,OAAO;IACP,MAAM;IACN,UAAU;QAAC;QAAY;QAAW;KAAS;IAC3C,YAAY;QACV,QAAQ;YACN,MAAM;YACN,OAAO;QACT;QACA,UAAU;YACR,MAAM;YACN,UAAU;gBAAC;gBAAQ;gBAAe;gBAAW;gBAAQ;gBAAY;gBAAmB;gBAAQ;gBAAa;aAAY;YACrH,YAAY;gBACV,MAAM;oBACJ,MAAM;oBACN,WAAW;oBACX,WAAW;gBACb;gBACA,aAAa;oBACX,MAAM;oBACN,WAAW;oBACX,WAAW;gBACb;gBACA,SAAS;oBACP,MAAM;oBACN,SAAS;gBACX;gBACA,QAAQ;oBACN,MAAM;oBACN,WAAW;gBACb;gBACA,MAAM;oBACJ,MAAM;oBACN,OAAO;wBACL,MAAM;wBACN,WAAW;wBACX,WAAW;oBACb;oBACA,UAAU;gBACZ;gBACA,UAAU;oBACR,MAAM;oBACN,MAAM;wBAAC;wBAAQ;wBAAU;wBAAe;wBAAU;qBAAW;gBAC/D;gBACA,eAAe;oBACb,MAAM;oBACN,OAAO;wBACL,MAAM;wBACN,MAAM;4BACJ;4BAAU;4BAAe;4BAAe;4BACxC;4BAAqB;4BAAiB;4BACtC;4BAAc;4BAAgB;4BAAiB;yBAChD;oBACH;gBACF;gBACA,iBAAiB;oBACf,MAAM;oBACN,MAAM;wBAAC;wBAAY;wBAAa;wBAAQ;qBAAO;gBACjD;gBACA,MAAM;oBACJ,MAAM;oBACN,MAAM;wBAAC;wBAAU;wBAAW;qBAAW;gBACzC;gBACA,mBAAmB;oBACjB,MAAM;oBACN,YAAY;wBACV,SAAS;4BAAE,MAAM;wBAAU;wBAC3B,UAAU;4BAAE,MAAM;wBAAU;wBAC5B,SAAS;4BAAE,MAAM;wBAAU;oBAC7B;oBACA,sBAAsB;gBACxB;gBACA,WAAW;oBACT,MAAM;oBACN,QAAQ;gBACV;gBACA,WAAW;oBACT,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,sBAAsB;QACxB;QACA,SAAS;YACP,MAAM;YACN,UAAU;gBAAC;gBAAc;aAAY;YACrC,YAAY;gBACV,cAAc;oBACZ,MAAM;oBACN,WAAW;gBACb;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,WAAW;gBACb;gBACA,iBAAiB;oBACf,MAAM;oBACN,WAAW;gBACb;gBACA,WAAW;oBACT,MAAM;oBACN,OAAO;wBACL,MAAM;wBACN,UAAU;4BAAC;4BAAQ;yBAAO;wBAC1B,YAAY;4BACV,MAAM;gCACJ,MAAM;gCACN,SAAS;gCACT,WAAW;gCACX,WAAW;4BACb;4BACA,MAAM;gCACJ,MAAM;gCACN,MAAM;oCAAC;oCAAQ;oCAAU;oCAAW;oCAAU;iCAAc;4BAC9D;4BACA,aAAa;gCACX,MAAM;gCACN,WAAW;4BACb;4BACA,UAAU;gCACR,MAAM;gCACN,SAAS;4BACX;4BACA,cAAc;gCACZ,OAAO;oCACL;wCAAE,MAAM;oCAAS;oCACjB;wCAAE,MAAM;oCAAS;oCACjB;wCAAE,MAAM;oCAAU;iCACnB;4BACH;4BACA,SAAS;gCACP,MAAM;gCACN,OAAO;oCACL,MAAM;oCACN,WAAW;oCACX,WAAW;gCACb;gCACA,UAAU;4BACZ;4BACA,aAAa;gCACX,MAAM;gCACN,WAAW;4BACb;wBACF;wBACA,sBAAsB;oBACxB;gBACF;gBACA,YAAY;oBACV,MAAM;oBACN,SAAS;oBACT,SAAS;gBACX;gBACA,kBAAkB;oBAChB,MAAM;oBACN,SAAS;gBACX;YACF;YACA,sBAAsB;QACxB;IACF;IACA,sBAAsB;AACxB;AAeO,MAAM,2BAA2B,CAAC;IACvC,MAAM,MAAM,IAAI,OAAO,WAAW;IAElC,OAAO;QACL,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,MAAM,EAAE;YACR,UAAU;YACV,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;YACX,GAAG,WAAW,QAAQ;QACxB;QACA,SAAS;YACP,YAAY;YACZ,WAAW,EAAE;YACb,kBAAkB;YAClB,GAAG,WAAW,OAAO;QACvB;QACA,GAAG,SAAS;IACd;AACF;AAGO,MAAM,mBAAoD;IAC/D,OAAO;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,SAAS;YAAC;YAAgB;YAAU;YAAY;YAAU;YAAY;SAAY;IACpF;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,aAAa;IACf;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,SAAS;YAAC;YAAS;YAAU;YAAQ;SAAW;IAClD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,SAAS;YAAC;YAAY;YAAa;YAAQ;YAAQ;YAAiB;SAAgB;IACtF;AACF", "debugId": null}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/lib/promptx.ts"], "sourcesContent": ["// .promptx Module System Core Functions\n\nimport { \n  PromptxModule, \n  PromptxValidationResult, \n  PromptxValidationError,\n  PROMPTX_SCHEMA,\n  createEmptyPromptxModule,\n  PromptxVariable\n} from '@/types/promptx';\n\n// JSON Schema validation using Ajv-like validation\nexport function validatePromptxModule(module: any): PromptxValidationResult {\n  const errors: PromptxValidationError[] = [];\n\n  try {\n    // Basic structure validation\n    if (!module || typeof module !== 'object') {\n      errors.push({\n        path: 'root',\n        message: 'Module must be a valid object',\n        value: module\n      });\n      return { valid: false, errors };\n    }\n\n    // Schema version check\n    if (module.schema !== '1.0.0') {\n      errors.push({\n        path: 'schema',\n        message: 'Schema version must be \"1.0.0\"',\n        value: module.schema\n      });\n    }\n\n    // Metadata validation\n    if (!module.metadata) {\n      errors.push({\n        path: 'metadata',\n        message: 'Metadata is required',\n        value: module.metadata\n      });\n    } else {\n      validateMetadata(module.metadata, errors);\n    }\n\n    // Content validation\n    if (!module.content) {\n      errors.push({\n        path: 'content',\n        message: 'Content is required',\n        value: module.content\n      });\n    } else {\n      validateContent(module.content, errors);\n    }\n\n  } catch (error) {\n    errors.push({\n      path: 'validation',\n      message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n      value: error\n    });\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors\n  };\n}\n\nfunction validateMetadata(metadata: any, errors: PromptxValidationError[]) {\n  const required = ['name', 'description', 'version', 'tags', 'category', 'formattingStyle', 'type', 'createdAt', 'updatedAt'];\n  \n  for (const field of required) {\n    if (!metadata[field]) {\n      errors.push({\n        path: `metadata.${field}`,\n        message: `${field} is required`,\n        value: metadata[field]\n      });\n    }\n  }\n\n  // Validate specific fields\n  if (metadata.name && (typeof metadata.name !== 'string' || metadata.name.length === 0 || metadata.name.length > 100)) {\n    errors.push({\n      path: 'metadata.name',\n      message: 'Name must be a string between 1 and 100 characters',\n      value: metadata.name\n    });\n  }\n\n  if (metadata.version && !/^\\d+\\.\\d+\\.\\d+$/.test(metadata.version)) {\n    errors.push({\n      path: 'metadata.version',\n      message: 'Version must follow semantic versioning (e.g., \"1.0.0\")',\n      value: metadata.version\n    });\n  }\n\n  if (metadata.category && !['core', 'engine', 'specialized', 'custom', 'template'].includes(metadata.category)) {\n    errors.push({\n      path: 'metadata.category',\n      message: 'Category must be one of: core, engine, specialized, custom, template',\n      value: metadata.category\n    });\n  }\n\n  if (metadata.formattingStyle && !['markdown', 'plaintext', 'json', 'html'].includes(metadata.formattingStyle)) {\n    errors.push({\n      path: 'metadata.formattingStyle',\n      message: 'Formatting style must be one of: markdown, plaintext, json, html',\n      value: metadata.formattingStyle\n    });\n  }\n\n  if (metadata.type && !['single', 'chained', 'template'].includes(metadata.type)) {\n    errors.push({\n      path: 'metadata.type',\n      message: 'Type must be one of: single, chained, template',\n      value: metadata.type\n    });\n  }\n}\n\nfunction validateContent(content: any, errors: PromptxValidationError[]) {\n  if (!content.userPrompt || typeof content.userPrompt !== 'string' || content.userPrompt.length === 0) {\n    errors.push({\n      path: 'content.userPrompt',\n      message: 'User prompt is required and must be a non-empty string',\n      value: content.userPrompt\n    });\n  }\n\n  if (!Array.isArray(content.variables)) {\n    errors.push({\n      path: 'content.variables',\n      message: 'Variables must be an array',\n      value: content.variables\n    });\n  } else {\n    content.variables.forEach((variable: any, index: number) => {\n      validateVariable(variable, `content.variables[${index}]`, errors);\n    });\n  }\n}\n\nfunction validateVariable(variable: any, path: string, errors: PromptxValidationError[]) {\n  if (!variable.name || typeof variable.name !== 'string') {\n    errors.push({\n      path: `${path}.name`,\n      message: 'Variable name is required and must be a string',\n      value: variable.name\n    });\n  } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(variable.name)) {\n    errors.push({\n      path: `${path}.name`,\n      message: 'Variable name must start with a letter and contain only letters, numbers, and underscores',\n      value: variable.name\n    });\n  }\n\n  if (!variable.type || !['text', 'number', 'boolean', 'select', 'multiselect'].includes(variable.type)) {\n    errors.push({\n      path: `${path}.type`,\n      message: 'Variable type must be one of: text, number, boolean, select, multiselect',\n      value: variable.type\n    });\n  }\n}\n\n// Parse .promptx file content\nexport function parsePromptxFile(content: string): { module: PromptxModule | null; errors: PromptxValidationError[] } {\n  try {\n    const parsed = JSON.parse(content);\n    const validation = validatePromptxModule(parsed);\n    \n    if (validation.valid) {\n      return { module: parsed as PromptxModule, errors: [] };\n    } else {\n      return { module: null, errors: validation.errors };\n    }\n  } catch (error) {\n    return {\n      module: null,\n      errors: [{\n        path: 'parse',\n        message: `Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        value: content\n      }]\n    };\n  }\n}\n\n// Serialize .promptx module to file content\nexport function serializePromptxModule(module: PromptxModule): string {\n  // Update the updatedAt timestamp\n  const updatedModule = {\n    ...module,\n    metadata: {\n      ...module.metadata,\n      updatedAt: new Date().toISOString()\n    }\n  };\n\n  return JSON.stringify(updatedModule, null, 2);\n}\n\n// Variable substitution in prompts\nexport function substituteVariables(\n  prompt: string, \n  variables: Record<string, any>\n): string {\n  let result = prompt;\n  \n  // Replace {{variableName}} with actual values\n  for (const [name, value] of Object.entries(variables)) {\n    const regex = new RegExp(`\\\\{\\\\{\\\\s*${name}\\\\s*\\\\}\\\\}`, 'g');\n    result = result.replace(regex, String(value || ''));\n  }\n  \n  return result;\n}\n\n// Extract variable names from prompt text\nexport function extractVariableNames(prompt: string): string[] {\n  const regex = /\\{\\{\\s*([a-zA-Z][a-zA-Z0-9_]*)\\s*\\}\\}/g;\n  const matches = [];\n  let match;\n  \n  while ((match = regex.exec(prompt)) !== null) {\n    if (!matches.includes(match[1])) {\n      matches.push(match[1]);\n    }\n  }\n  \n  return matches;\n}\n\n// Generate a complete prompt from module and variable values\nexport function generatePrompt(\n  module: PromptxModule,\n  variableValues: Record<string, any>\n): {\n  systemPrompt?: string;\n  userPrompt: string;\n  assistantPrompt?: string;\n} {\n  const { content } = module;\n  \n  return {\n    systemPrompt: content.systemPrompt ? substituteVariables(content.systemPrompt, variableValues) : undefined,\n    userPrompt: substituteVariables(content.userPrompt, variableValues),\n    assistantPrompt: content.assistantPrompt ? substituteVariables(content.assistantPrompt, variableValues) : undefined\n  };\n}\n\n// Create module from template with common patterns\nexport function createModuleFromTemplate(\n  name: string,\n  template: 'writer' | 'analyzer' | 'coder' | 'qa' | 'custom',\n  customPrompt?: string\n): PromptxModule {\n  const baseModule = createEmptyPromptxModule();\n  \n  switch (template) {\n    case 'writer':\n      return {\n        ...baseModule,\n        metadata: {\n          ...baseModule.metadata,\n          name: name || 'Writing Assistant',\n          description: 'Helps create high-quality written content',\n          category: 'core',\n          tags: ['writing', 'content', 'assistant']\n        },\n        content: {\n          ...baseModule.content,\n          systemPrompt: 'You are an expert writing assistant. Help create clear, engaging, and well-structured content.',\n          userPrompt: 'Write {{format}} about {{topic}} in a {{tone}} tone for {{audience}}. {{additional_instructions}}',\n          variables: [\n            { name: 'topic', type: 'text', description: 'Main topic', required: true },\n            { name: 'format', type: 'select', options: ['article', 'blog post', 'essay', 'summary'], defaultValue: 'article' },\n            { name: 'tone', type: 'select', options: ['professional', 'casual', 'academic', 'creative'], defaultValue: 'professional' },\n            { name: 'audience', type: 'text', description: 'Target audience', placeholder: 'e.g., professionals, students' },\n            { name: 'additional_instructions', type: 'text', description: 'Any additional requirements', required: false }\n          ]\n        }\n      };\n      \n    case 'analyzer':\n      return {\n        ...baseModule,\n        metadata: {\n          ...baseModule.metadata,\n          name: name || 'Content Analyzer',\n          description: 'Analyzes and provides insights on content',\n          category: 'specialized',\n          tags: ['analysis', 'insights', 'review']\n        },\n        content: {\n          ...baseModule.content,\n          systemPrompt: 'You are an expert analyst. Provide thorough, objective analysis with actionable insights.',\n          userPrompt: 'Analyze the following {{content_type}}: {{content}}\\n\\nFocus on: {{analysis_focus}}',\n          variables: [\n            { name: 'content_type', type: 'select', options: ['text', 'code', 'data', 'document'], defaultValue: 'text' },\n            { name: 'content', type: 'text', description: 'Content to analyze', required: true },\n            { name: 'analysis_focus', type: 'text', description: 'What to focus on', placeholder: 'e.g., structure, clarity, performance' }\n          ]\n        }\n      };\n      \n    case 'custom':\n      return {\n        ...baseModule,\n        metadata: {\n          ...baseModule.metadata,\n          name: name || 'Custom Module',\n          description: 'A custom prompt module',\n          category: 'custom'\n        },\n        content: {\n          ...baseModule.content,\n          userPrompt: customPrompt || 'Enter your custom prompt here...'\n        }\n      };\n      \n    default:\n      return baseModule;\n  }\n}\n\n// Export/Import utilities\nexport const PromptxUtils = {\n  validate: validatePromptxModule,\n  parse: parsePromptxFile,\n  serialize: serializePromptxModule,\n  substitute: substituteVariables,\n  extract: extractVariableNames,\n  generate: generatePrompt,\n  createFromTemplate: createModuleFromTemplate,\n  createEmpty: createEmptyPromptxModule\n};\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;AAExC;;AAUO,SAAS,sBAAsB,MAAW;IAC/C,MAAM,SAAmC,EAAE;IAE3C,IAAI;QACF,6BAA6B;QAC7B,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;YACzC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,OAAO;gBAAE,OAAO;gBAAO;YAAO;QAChC;QAEA,uBAAuB;QACvB,IAAI,OAAO,MAAM,KAAK,SAAS;YAC7B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO,OAAO,MAAM;YACtB;QACF;QAEA,sBAAsB;QACtB,IAAI,CAAC,OAAO,QAAQ,EAAE;YACpB,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO,OAAO,QAAQ;YACxB;QACF,OAAO;YACL,iBAAiB,OAAO,QAAQ,EAAE;QACpC;QAEA,qBAAqB;QACrB,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO,OAAO,OAAO;YACvB;QACF,OAAO;YACL,gBAAgB,OAAO,OAAO,EAAE;QAClC;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YACxF,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAEA,SAAS,iBAAiB,QAAa,EAAE,MAAgC;IACvE,MAAM,WAAW;QAAC;QAAQ;QAAe;QAAW;QAAQ;QAAY;QAAmB;QAAQ;QAAa;KAAY;IAE5H,KAAK,MAAM,SAAS,SAAU;QAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,OAAO,IAAI,CAAC;gBACV,MAAM,CAAC,SAAS,EAAE,OAAO;gBACzB,SAAS,GAAG,MAAM,YAAY,CAAC;gBAC/B,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF;IACF;IAEA,2BAA2B;IAC3B,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,SAAS,IAAI,KAAK,YAAY,SAAS,IAAI,CAAC,MAAM,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG;QACpH,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,IAAI,SAAS,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,OAAO,GAAG;QACjE,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,SAAS,OAAO;QACzB;IACF;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC;QAAC;QAAQ;QAAU;QAAe;QAAU;KAAW,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAAG;QAC7G,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,SAAS,QAAQ;QAC1B;IACF;IAEA,IAAI,SAAS,eAAe,IAAI,CAAC;QAAC;QAAY;QAAa;QAAQ;KAAO,CAAC,QAAQ,CAAC,SAAS,eAAe,GAAG;QAC7G,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,SAAS,eAAe;QACjC;IACF;IAEA,IAAI,SAAS,IAAI,IAAI,CAAC;QAAC;QAAU;QAAW;KAAW,CAAC,QAAQ,CAAC,SAAS,IAAI,GAAG;QAC/E,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,SAAS,IAAI;QACtB;IACF;AACF;AAEA,SAAS,gBAAgB,OAAY,EAAE,MAAgC;IACrE,IAAI,CAAC,QAAQ,UAAU,IAAI,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;QACpG,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,QAAQ,UAAU;QAC3B;IACF;IAEA,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,SAAS,GAAG;QACrC,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO,QAAQ,SAAS;QAC1B;IACF,OAAO;QACL,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,UAAe;YACxC,iBAAiB,UAAU,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC,EAAE;QAC5D;IACF;AACF;AAEA,SAAS,iBAAiB,QAAa,EAAE,IAAY,EAAE,MAAgC;IACrF,IAAI,CAAC,SAAS,IAAI,IAAI,OAAO,SAAS,IAAI,KAAK,UAAU;QACvD,OAAO,IAAI,CAAC;YACV,MAAM,GAAG,KAAK,KAAK,CAAC;YACpB,SAAS;YACT,OAAO,SAAS,IAAI;QACtB;IACF,OAAO,IAAI,CAAC,0BAA0B,IAAI,CAAC,SAAS,IAAI,GAAG;QACzD,OAAO,IAAI,CAAC;YACV,MAAM,GAAG,KAAK,KAAK,CAAC;YACpB,SAAS;YACT,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;QAAC;QAAQ;QAAU;QAAW;QAAU;KAAc,CAAC,QAAQ,CAAC,SAAS,IAAI,GAAG;QACrG,OAAO,IAAI,CAAC;YACV,MAAM,GAAG,KAAK,KAAK,CAAC;YACpB,SAAS;YACT,OAAO,SAAS,IAAI;QACtB;IACF;AACF;AAGO,SAAS,iBAAiB,OAAe;IAC9C,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,MAAM,aAAa,sBAAsB;QAEzC,IAAI,WAAW,KAAK,EAAE;YACpB,OAAO;gBAAE,QAAQ;gBAAyB,QAAQ,EAAE;YAAC;QACvD,OAAO;YACL,OAAO;gBAAE,QAAQ;gBAAM,QAAQ,WAAW,MAAM;YAAC;QACnD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,QAAQ;gBAAC;oBACP,MAAM;oBACN,SAAS,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;oBAC5F,OAAO;gBACT;aAAE;QACJ;IACF;AACF;AAGO,SAAS,uBAAuB,MAAqB;IAC1D,iCAAiC;IACjC,MAAM,gBAAgB;QACpB,GAAG,MAAM;QACT,UAAU;YACR,GAAG,OAAO,QAAQ;YAClB,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,OAAO,KAAK,SAAS,CAAC,eAAe,MAAM;AAC7C;AAGO,SAAS,oBACd,MAAc,EACd,SAA8B;IAE9B,IAAI,SAAS;IAEb,8CAA8C;IAC9C,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,WAAY;QACrD,MAAM,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,UAAU,CAAC,EAAE;QACxD,SAAS,OAAO,OAAO,CAAC,OAAO,OAAO,SAAS;IACjD;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,MAAM,UAAU,EAAE;IAClB,IAAI;IAEJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,OAAO,MAAM,KAAM;QAC5C,IAAI,CAAC,QAAQ,QAAQ,CAAC,KAAK,CAAC,EAAE,GAAG;YAC/B,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;QACvB;IACF;IAEA,OAAO;AACT;AAGO,SAAS,eACd,MAAqB,EACrB,cAAmC;IAMnC,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,OAAO;QACL,cAAc,QAAQ,YAAY,GAAG,oBAAoB,QAAQ,YAAY,EAAE,kBAAkB;QACjG,YAAY,oBAAoB,QAAQ,UAAU,EAAE;QACpD,iBAAiB,QAAQ,eAAe,GAAG,oBAAoB,QAAQ,eAAe,EAAE,kBAAkB;IAC5G;AACF;AAGO,SAAS,yBACd,IAAY,EACZ,QAA2D,EAC3D,YAAqB;IAErB,MAAM,aAAa,CAAA,GAAA,uHAAA,CAAA,2BAAwB,AAAD;IAE1C,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU;oBACR,GAAG,WAAW,QAAQ;oBACtB,MAAM,QAAQ;oBACd,aAAa;oBACb,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAW;qBAAY;gBAC3C;gBACA,SAAS;oBACP,GAAG,WAAW,OAAO;oBACrB,cAAc;oBACd,YAAY;oBACZ,WAAW;wBACT;4BAAE,MAAM;4BAAS,MAAM;4BAAQ,aAAa;4BAAc,UAAU;wBAAK;wBACzE;4BAAE,MAAM;4BAAU,MAAM;4BAAU,SAAS;gCAAC;gCAAW;gCAAa;gCAAS;6BAAU;4BAAE,cAAc;wBAAU;wBACjH;4BAAE,MAAM;4BAAQ,MAAM;4BAAU,SAAS;gCAAC;gCAAgB;gCAAU;gCAAY;6BAAW;4BAAE,cAAc;wBAAe;wBAC1H;4BAAE,MAAM;4BAAY,MAAM;4BAAQ,aAAa;4BAAmB,aAAa;wBAAgC;wBAC/G;4BAAE,MAAM;4BAA2B,MAAM;4BAAQ,aAAa;4BAA+B,UAAU;wBAAM;qBAC9G;gBACH;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU;oBACR,GAAG,WAAW,QAAQ;oBACtB,MAAM,QAAQ;oBACd,aAAa;oBACb,UAAU;oBACV,MAAM;wBAAC;wBAAY;wBAAY;qBAAS;gBAC1C;gBACA,SAAS;oBACP,GAAG,WAAW,OAAO;oBACrB,cAAc;oBACd,YAAY;oBACZ,WAAW;wBACT;4BAAE,MAAM;4BAAgB,MAAM;4BAAU,SAAS;gCAAC;gCAAQ;gCAAQ;gCAAQ;6BAAW;4BAAE,cAAc;wBAAO;wBAC5G;4BAAE,MAAM;4BAAW,MAAM;4BAAQ,aAAa;4BAAsB,UAAU;wBAAK;wBACnF;4BAAE,MAAM;4BAAkB,MAAM;4BAAQ,aAAa;4BAAoB,aAAa;wBAAwC;qBAC/H;gBACH;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU;oBACR,GAAG,WAAW,QAAQ;oBACtB,MAAM,QAAQ;oBACd,aAAa;oBACb,UAAU;gBACZ;gBACA,SAAS;oBACP,GAAG,WAAW,OAAO;oBACrB,YAAY,gBAAgB;gBAC9B;YACF;QAEF;YACE,OAAO;IACX;AACF;AAGO,MAAM,eAAe;IAC1B,UAAU;IACV,OAAO;IACP,WAAW;IACX,YAAY;IACZ,SAAS;IACT,UAAU;IACV,oBAAoB;IACpB,aAAa,uHAAA,CAAA,2BAAwB;AACvC", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/modules/module-manager.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useRef } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Input } from '@/components/ui/input';\nimport {\n  Upload,\n  Download,\n  Plus,\n  FileText,\n  Search,\n  Edit,\n  Trash2,\n  Copy,\n  Play\n} from 'lucide-react';\nimport { PromptxModule, PromptxValidationError } from '@/types/promptx';\nimport { PromptxUtils } from '@/lib/promptx';\n\ninterface ModuleManagerProps {\n  modules: PromptxModule[];\n  onModuleLoad: (module: PromptxModule) => void;\n  onModuleCreate: (template: string) => void;\n  onModuleEdit: (module: PromptxModule) => void;\n  onModuleDelete: (moduleId: string) => void;\n  onModuleExecute: (module: PromptxModule) => void;\n}\n\nexport function ModuleManager({\n  modules,\n  onModuleLoad,\n  onModuleCreate,\n  onModuleEdit,\n  onModuleDelete,\n  onModuleExecute\n}: ModuleManagerProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [importErrors, setImportErrors] = useState<PromptxValidationError[]>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Filter modules based on search and category\n  const filteredModules = modules.filter(module => {\n    const matchesSearch = module.metadata.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         module.metadata.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         module.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    \n    const matchesCategory = selectedCategory === 'all' || module.metadata.category === selectedCategory;\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  // Handle file import\n  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      const { module, errors } = PromptxUtils.parse(content);\n      \n      if (module) {\n        onModuleLoad(module);\n        setImportErrors([]);\n      } else {\n        setImportErrors(errors);\n      }\n    };\n    reader.readAsText(file);\n    \n    // Reset file input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle module export\n  const handleModuleExport = (module: PromptxModule) => {\n    const content = PromptxUtils.serialize(module);\n    const blob = new Blob([content], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    \n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${module.metadata.name.replace(/[^a-zA-Z0-9]/g, '_')}.promptx`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // Get category badge color\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'core': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';\n      case 'engine': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';\n      case 'specialized': return 'bg-green-500/20 text-green-400 border-green-500/30';\n      case 'custom': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';\n      case 'template': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';\n      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';\n    }\n  };\n\n  // Get type icon\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'single': return '📄';\n      case 'chained': return '🔗';\n      case 'template': return '📋';\n      default: return '📄';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Actions */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex flex-col sm:flex-row gap-3 flex-1\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <Input\n              placeholder=\"Search modules...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          \n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-3 py-2 bg-card border border-border rounded-md text-sm\"\n          >\n            <option value=\"all\">All Categories</option>\n            <option value=\"core\">Core</option>\n            <option value=\"engine\">Engine</option>\n            <option value=\"specialized\">Specialized</option>\n            <option value=\"custom\">Custom</option>\n            <option value=\"template\">Template</option>\n          </select>\n        </div>\n\n        <div className=\"flex gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => fileInputRef.current?.click()}\n            className=\"catalyst-border\"\n          >\n            <Upload className=\"w-4 h-4 mr-2\" />\n            Import\n          </Button>\n          \n          <Button\n            size=\"sm\"\n            onClick={() => onModuleCreate('custom')}\n            className=\"catalyst-gradient-bg\"\n          >\n            <Plus className=\"w-4 h-4 mr-2\" />\n            New Module\n          </Button>\n        </div>\n      </div>\n\n      {/* Import Errors */}\n      {importErrors.length > 0 && (\n        <Card className=\"border-red-500/50 bg-red-500/10\">\n          <CardHeader>\n            <CardTitle className=\"text-red-400 text-sm\">Import Errors</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-1\">\n              {importErrors.map((error, index) => (\n                <div key={index} className=\"text-sm text-red-300\">\n                  <span className=\"font-mono text-xs\">{error.path}:</span> {error.message}\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Module Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {filteredModules.map((module) => (\n          <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className=\"module-card group\">\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-lg\">{getTypeIcon(module.metadata.type)}</span>\n                  <div>\n                    <CardTitle className=\"text-sm font-medium line-clamp-1\">\n                      {module.metadata.name}\n                    </CardTitle>\n                    <CardDescription className=\"text-xs line-clamp-2 mt-1\">\n                      {module.metadata.description}\n                    </CardDescription>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex flex-wrap gap-1 mt-2\">\n                <Badge className={getCategoryColor(module.metadata.category)}>\n                  {module.metadata.category}\n                </Badge>\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  v{module.metadata.version}\n                </Badge>\n                {module.metadata.modelAffinity && module.metadata.modelAffinity.length > 0 && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {module.metadata.modelAffinity[0]}\n                    {module.metadata.modelAffinity.length > 1 && ` +${module.metadata.modelAffinity.length - 1}`}\n                  </Badge>\n                )}\n              </div>\n            </CardHeader>\n            \n            <CardContent className=\"pt-0\">\n              <div className=\"flex flex-wrap gap-1 mb-3\">\n                {module.metadata.tags.slice(0, 3).map((tag) => (\n                  <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                    {tag}\n                  </Badge>\n                ))}\n                {module.metadata.tags.length > 3 && (\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    +{module.metadata.tags.length - 3}\n                  </Badge>\n                )}\n              </div>\n              \n              <div className=\"text-xs text-muted-foreground mb-3\">\n                {module.content.variables.length} variable{module.content.variables.length !== 1 ? 's' : ''}\n                {module.metadata.author && ` • by ${module.metadata.author}`}\n              </div>\n              \n              <div className=\"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={() => onModuleExecute(module)}\n                  className=\"h-8 px-2\"\n                >\n                  <Play className=\"w-3 h-3\" />\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={() => onModuleEdit(module)}\n                  className=\"h-8 px-2\"\n                >\n                  <Edit className=\"w-3 h-3\" />\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={() => handleModuleExport(module)}\n                  className=\"h-8 px-2\"\n                >\n                  <Download className=\"w-3 h-3\" />\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"ghost\"\n                  onClick={() => navigator.clipboard.writeText(PromptxUtils.serialize(module))}\n                  className=\"h-8 px-2\"\n                >\n                  <Copy className=\"w-3 h-3\" />\n                </Button>\n                {module.metadata.category === 'custom' && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"ghost\"\n                    onClick={() => onModuleDelete(`${module.metadata.name}-${module.metadata.createdAt}`)}\n                    className=\"h-8 px-2 text-red-400 hover:text-red-300\"\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </Button>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {filteredModules.length === 0 && (\n        <Card className=\"text-center py-12\">\n          <CardContent>\n            <FileText className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\n            <CardTitle className=\"text-lg mb-2\">No modules found</CardTitle>\n            <CardDescription className=\"mb-4\">\n              {searchTerm || selectedCategory !== 'all' \n                ? 'Try adjusting your search or filter criteria'\n                : 'Create your first module or import existing ones'\n              }\n            </CardDescription>\n            <div className=\"flex gap-2 justify-center\">\n              <Button onClick={() => onModuleCreate('custom')}>\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Create Module\n              </Button>\n              <Button variant=\"outline\" onClick={() => fileInputRef.current?.click()}>\n                <Upload className=\"w-4 h-4 mr-2\" />\n                Import Module\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Hidden file input */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\".promptx,.json\"\n        onChange={handleFileImport}\n        className=\"hidden\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;;;;AA8BO,SAAS,cAAc,EAC5B,OAAO,EACP,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,cAAc,EACd,eAAe,EACI;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7E,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,8CAA8C;IAC9C,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,OAAO,QAAQ,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvG,MAAM,kBAAkB,qBAAqB,SAAS,OAAO,QAAQ,CAAC,QAAQ,KAAK;QAEnF,OAAO,iBAAiB;IAC1B;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,qHAAA,CAAA,eAAY,CAAC,KAAK,CAAC;YAE9C,IAAI,QAAQ;gBACV,aAAa;gBACb,gBAAgB,EAAE;YACpB,OAAO;gBACL,gBAAgB;YAClB;QACF;QACA,OAAO,UAAU,CAAC;QAElB,mBAAmB;QACnB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,UAAU,qHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;QACvC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAC5D,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,QAAQ,CAAC;QAC5E,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,gBAAgB;IAChB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,aAAa,MAAM,GAAG,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAuB;;;;;;;;;;;kCAE9C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAK,WAAU;;gDAAqB,MAAM,IAAI;gDAAC;;;;;;;wCAAQ;wCAAE,MAAM,OAAO;;mCAD/D;;;;;;;;;;;;;;;;;;;;;0BAUpB,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,gIAAA,CAAA,OAAI;wBAA8D,WAAU;;0CAC3E,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAW,YAAY,OAAO,QAAQ,CAAC,IAAI;;;;;;8DAC3D,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,OAAO,QAAQ,CAAC,IAAI;;;;;;sEAEvB,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,OAAO,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,iBAAiB,OAAO,QAAQ,CAAC,QAAQ;0DACxD,OAAO,QAAQ,CAAC,QAAQ;;;;;;0DAE3B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAU;oDACzC,OAAO,QAAQ,CAAC,OAAO;;;;;;;4CAE1B,OAAO,QAAQ,CAAC,aAAa,IAAI,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,mBACvE,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC,OAAO,QAAQ,CAAC,aAAa,CAAC,EAAE;oDAChC,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG;;;;;;;;;;;;;;;;;;;0CAMpG,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrC,8OAAC,iIAAA,CAAA,QAAK;oDAAW,SAAQ;oDAAY,WAAU;8DAC5C;mDADS;;;;;4CAIb,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,mBAC7B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;oDAAU;oDAC3C,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;kDAKtC,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;4CAAC;4CAAU,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,MAAM;4CACxF,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE;;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,qHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;gDACpE,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;4CAEjB,OAAO,QAAQ,CAAC,QAAQ,KAAK,0BAC5B,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE;gDACpF,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBA3FjB,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE;;;;;;;;;;YAqGpE,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAe;;;;;;sCACpC,8OAAC,gIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,cAAc,qBAAqB,QAChC,iDACA;;;;;;sCAGN,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,eAAe;;sDACpC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,aAAa,OAAO,EAAE;;sDAC7D,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,UAAU;gBACV,WAAU;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 2522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/modules/module-editor.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  Save, \n  X, \n  Plus, \n  Trash2, \n  Eye, \n  Code,\n  Settings,\n  Play,\n  Download\n} from 'lucide-react';\nimport { PromptxModule, PromptxVariable, COMMON_VARIABLES } from '@/types/promptx';\nimport { PromptxUtils } from '@/lib/promptx';\n\ninterface ModuleEditorProps {\n  module: PromptxModule | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (module: PromptxModule) => void;\n  onExecute?: (module: PromptxModule) => void;\n}\n\nexport function ModuleEditor({ module, isOpen, onClose, onSave, onExecute }: ModuleEditorProps) {\n  const [editedModule, setEditedModule] = useState<PromptxModule | null>(null);\n  const [previewValues, setPreviewValues] = useState<Record<string, any>>({});\n  const [activeTab, setActiveTab] = useState('content');\n  const [validationErrors, setValidationErrors] = useState<string[]>([]);\n\n  useEffect(() => {\n    if (module) {\n      setEditedModule({ ...module });\n      // Initialize preview values with defaults\n      const defaultValues: Record<string, any> = {};\n      module.content.variables.forEach(variable => {\n        if (variable.defaultValue !== undefined) {\n          defaultValues[variable.name] = variable.defaultValue;\n        }\n      });\n      setPreviewValues(defaultValues);\n    }\n  }, [module]);\n\n  if (!isOpen || !editedModule) return null;\n\n  const handleSave = () => {\n    const validation = PromptxUtils.validate(editedModule);\n    if (validation.valid) {\n      onSave(editedModule);\n      onClose();\n    } else {\n      setValidationErrors(validation.errors.map(e => `${e.path}: ${e.message}`));\n    }\n  };\n\n  const handleMetadataChange = (field: string, value: any) => {\n    setEditedModule(prev => prev ? {\n      ...prev,\n      metadata: {\n        ...prev.metadata,\n        [field]: value,\n        updatedAt: new Date().toISOString()\n      }\n    } : null);\n  };\n\n  const handleContentChange = (field: string, value: any) => {\n    setEditedModule(prev => prev ? {\n      ...prev,\n      content: {\n        ...prev.content,\n        [field]: value\n      }\n    } : null);\n  };\n\n  const addVariable = (template?: PromptxVariable) => {\n    const newVariable: PromptxVariable = template || {\n      name: 'newVariable',\n      type: 'text',\n      description: 'New variable',\n      required: false\n    };\n\n    setEditedModule(prev => prev ? {\n      ...prev,\n      content: {\n        ...prev.content,\n        variables: [...prev.content.variables, newVariable]\n      }\n    } : null);\n  };\n\n  const updateVariable = (index: number, field: string, value: any) => {\n    setEditedModule(prev => prev ? {\n      ...prev,\n      content: {\n        ...prev.content,\n        variables: prev.content.variables.map((variable, i) => \n          i === index ? { ...variable, [field]: value } : variable\n        )\n      }\n    } : null);\n  };\n\n  const removeVariable = (index: number) => {\n    setEditedModule(prev => prev ? {\n      ...prev,\n      content: {\n        ...prev.content,\n        variables: prev.content.variables.filter((_, i) => i !== index)\n      }\n    } : null);\n  };\n\n  const generatePreview = () => {\n    if (!editedModule) return { userPrompt: '', systemPrompt: '' };\n    return PromptxUtils.generate(editedModule, previewValues);\n  };\n\n  const extractedVariables = PromptxUtils.extract(editedModule.content.userPrompt + (editedModule.content.systemPrompt || ''));\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n      <Card className=\"w-full max-w-6xl max-h-[90vh] overflow-hidden catalyst-border\">\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\n          <div>\n            <CardTitle className=\"text-xl\">Edit Module</CardTitle>\n            <CardDescription>\n              {editedModule.metadata.name} • {editedModule.metadata.type} module\n            </CardDescription>\n          </div>\n          <div className=\"flex gap-2\">\n            {onExecute && (\n              <Button size=\"sm\" onClick={() => onExecute(editedModule)}>\n                <Play className=\"w-4 h-4 mr-2\" />\n                Test\n              </Button>\n            )}\n            <Button size=\"sm\" variant=\"outline\" onClick={() => {\n              const content = PromptxUtils.serialize(editedModule);\n              const blob = new Blob([content], { type: 'application/json' });\n              const url = URL.createObjectURL(blob);\n              const a = document.createElement('a');\n              a.href = url;\n              a.download = `${editedModule.metadata.name.replace(/[^a-zA-Z0-9]/g, '_')}.promptx`;\n              a.click();\n              URL.revokeObjectURL(url);\n            }}>\n              <Download className=\"w-4 h-4 mr-2\" />\n              Export\n            </Button>\n            <Button size=\"sm\" onClick={handleSave}>\n              <Save className=\"w-4 h-4 mr-2\" />\n              Save\n            </Button>\n            <Button size=\"sm\" variant=\"ghost\" onClick={onClose}>\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"overflow-auto max-h-[calc(90vh-120px)]\">\n          {validationErrors.length > 0 && (\n            <Card className=\"border-red-500/50 bg-red-500/10 mb-4\">\n              <CardContent className=\"pt-4\">\n                <div className=\"text-sm text-red-300 space-y-1\">\n                  {validationErrors.map((error, index) => (\n                    <div key={index}>{error}</div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          <Tabs value={activeTab} onValueChange={setActiveTab}>\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"content\">\n                <Code className=\"w-4 h-4 mr-2\" />\n                Content\n              </TabsTrigger>\n              <TabsTrigger value=\"variables\">\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Variables\n              </TabsTrigger>\n              <TabsTrigger value=\"metadata\">\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Metadata\n              </TabsTrigger>\n              <TabsTrigger value=\"preview\">\n                <Eye className=\"w-4 h-4 mr-2\" />\n                Preview\n              </TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"content\" className=\"space-y-4\">\n              <div className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"systemPrompt\">System Prompt (Optional)</Label>\n                  <textarea\n                    id=\"systemPrompt\"\n                    value={editedModule.content.systemPrompt || ''}\n                    onChange={(e) => handleContentChange('systemPrompt', e.target.value)}\n                    className=\"w-full h-32 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm\"\n                    placeholder=\"Enter system prompt...\"\n                  />\n                </div>\n                \n                <div>\n                  <Label htmlFor=\"userPrompt\">User Prompt *</Label>\n                  <textarea\n                    id=\"userPrompt\"\n                    value={editedModule.content.userPrompt}\n                    onChange={(e) => handleContentChange('userPrompt', e.target.value)}\n                    className=\"w-full h-40 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm\"\n                    placeholder=\"Enter user prompt with {{variables}}...\"\n                  />\n                  <div className=\"mt-2 text-xs text-muted-foreground\">\n                    Detected variables: {extractedVariables.length > 0 ? extractedVariables.join(', ') : 'None'}\n                  </div>\n                </div>\n\n                <div>\n                  <Label htmlFor=\"assistantPrompt\">Assistant Prompt (Optional)</Label>\n                  <textarea\n                    id=\"assistantPrompt\"\n                    value={editedModule.content.assistantPrompt || ''}\n                    onChange={(e) => handleContentChange('assistantPrompt', e.target.value)}\n                    className=\"w-full h-24 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm\"\n                    placeholder=\"Enter assistant prompt for few-shot examples...\"\n                  />\n                </div>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"variables\" className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <h3 className=\"text-lg font-medium\">Variables</h3>\n                <div className=\"flex gap-2\">\n                  <select\n                    onChange={(e) => {\n                      if (e.target.value && COMMON_VARIABLES[e.target.value]) {\n                        addVariable(COMMON_VARIABLES[e.target.value]);\n                        e.target.value = '';\n                      }\n                    }}\n                    className=\"px-3 py-1 bg-card border border-border rounded text-sm\"\n                  >\n                    <option value=\"\">Add Common Variable</option>\n                    {Object.keys(COMMON_VARIABLES).map(key => (\n                      <option key={key} value={key}>{key}</option>\n                    ))}\n                  </select>\n                  <Button size=\"sm\" onClick={() => addVariable()}>\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    Add Variable\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                {editedModule.content.variables.map((variable, index) => (\n                  <Card key={index} className=\"p-4\">\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div>\n                        <Label>Name</Label>\n                        <Input\n                          value={variable.name}\n                          onChange={(e) => updateVariable(index, 'name', e.target.value)}\n                          placeholder=\"variableName\"\n                        />\n                      </div>\n                      <div>\n                        <Label>Type</Label>\n                        <select\n                          value={variable.type}\n                          onChange={(e) => updateVariable(index, 'type', e.target.value)}\n                          className=\"w-full px-3 py-2 bg-card border border-border rounded\"\n                        >\n                          <option value=\"text\">Text</option>\n                          <option value=\"number\">Number</option>\n                          <option value=\"boolean\">Boolean</option>\n                          <option value=\"select\">Select</option>\n                          <option value=\"multiselect\">Multi-select</option>\n                        </select>\n                      </div>\n                      <div className=\"col-span-2\">\n                        <Label>Description</Label>\n                        <Input\n                          value={variable.description || ''}\n                          onChange={(e) => updateVariable(index, 'description', e.target.value)}\n                          placeholder=\"Variable description\"\n                        />\n                      </div>\n                      {(variable.type === 'select' || variable.type === 'multiselect') && (\n                        <div className=\"col-span-2\">\n                          <Label>Options (comma-separated)</Label>\n                          <Input\n                            value={variable.options?.join(', ') || ''}\n                            onChange={(e) => updateVariable(index, 'options', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}\n                            placeholder=\"option1, option2, option3\"\n                          />\n                        </div>\n                      )}\n                      <div className=\"col-span-2 flex justify-between items-center\">\n                        <div className=\"flex items-center gap-4\">\n                          <label className=\"flex items-center gap-2\">\n                            <input\n                              type=\"checkbox\"\n                              checked={variable.required || false}\n                              onChange={(e) => updateVariable(index, 'required', e.target.checked)}\n                            />\n                            Required\n                          </label>\n                        </div>\n                        <Button\n                          size=\"sm\"\n                          variant=\"ghost\"\n                          onClick={() => removeVariable(index)}\n                          className=\"text-red-400 hover:text-red-300\"\n                        >\n                          <Trash2 className=\"w-4 h-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </Card>\n                ))}\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"metadata\" className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"name\">Name *</Label>\n                  <Input\n                    id=\"name\"\n                    value={editedModule.metadata.name}\n                    onChange={(e) => handleMetadataChange('name', e.target.value)}\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"version\">Version</Label>\n                  <Input\n                    id=\"version\"\n                    value={editedModule.metadata.version}\n                    onChange={(e) => handleMetadataChange('version', e.target.value)}\n                    placeholder=\"1.0.0\"\n                  />\n                </div>\n                <div className=\"col-span-2\">\n                  <Label htmlFor=\"description\">Description *</Label>\n                  <textarea\n                    id=\"description\"\n                    value={editedModule.metadata.description}\n                    onChange={(e) => handleMetadataChange('description', e.target.value)}\n                    className=\"w-full h-20 mt-2 p-3 bg-card border border-border rounded-md resize-none\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"category\">Category</Label>\n                  <select\n                    id=\"category\"\n                    value={editedModule.metadata.category}\n                    onChange={(e) => handleMetadataChange('category', e.target.value)}\n                    className=\"w-full px-3 py-2 bg-card border border-border rounded\"\n                  >\n                    <option value=\"core\">Core</option>\n                    <option value=\"engine\">Engine</option>\n                    <option value=\"specialized\">Specialized</option>\n                    <option value=\"custom\">Custom</option>\n                    <option value=\"template\">Template</option>\n                  </select>\n                </div>\n                <div>\n                  <Label htmlFor=\"formattingStyle\">Formatting Style</Label>\n                  <select\n                    id=\"formattingStyle\"\n                    value={editedModule.metadata.formattingStyle}\n                    onChange={(e) => handleMetadataChange('formattingStyle', e.target.value)}\n                    className=\"w-full px-3 py-2 bg-card border border-border rounded\"\n                  >\n                    <option value=\"markdown\">Markdown</option>\n                    <option value=\"plaintext\">Plain Text</option>\n                    <option value=\"json\">JSON</option>\n                    <option value=\"html\">HTML</option>\n                  </select>\n                </div>\n                <div className=\"col-span-2\">\n                  <Label htmlFor=\"tags\">Tags (comma-separated)</Label>\n                  <Input\n                    id=\"tags\"\n                    value={editedModule.metadata.tags.join(', ')}\n                    onChange={(e) => handleMetadataChange('tags', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}\n                    placeholder=\"tag1, tag2, tag3\"\n                  />\n                </div>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"preview\" className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"text-lg font-medium mb-4\">Variable Values</h3>\n                  <div className=\"space-y-3\">\n                    {editedModule.content.variables.map((variable) => (\n                      <div key={variable.name}>\n                        <Label>{variable.name} {variable.required && '*'}</Label>\n                        {variable.type === 'select' ? (\n                          <select\n                            value={previewValues[variable.name] || ''}\n                            onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.value }))}\n                            className=\"w-full px-3 py-2 bg-card border border-border rounded mt-1\"\n                          >\n                            <option value=\"\">Select...</option>\n                            {variable.options?.map(option => (\n                              <option key={option} value={option}>{option}</option>\n                            ))}\n                          </select>\n                        ) : variable.type === 'boolean' ? (\n                          <div className=\"mt-1\">\n                            <label className=\"flex items-center gap-2\">\n                              <input\n                                type=\"checkbox\"\n                                checked={previewValues[variable.name] || false}\n                                onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.checked }))}\n                              />\n                              {variable.description || variable.name}\n                            </label>\n                          </div>\n                        ) : (\n                          <Input\n                            type={variable.type === 'number' ? 'number' : 'text'}\n                            value={previewValues[variable.name] || ''}\n                            onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.value }))}\n                            placeholder={variable.placeholder || variable.description}\n                            className=\"mt-1\"\n                          />\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-medium mb-4\">Generated Prompt</h3>\n                  <div className=\"space-y-4\">\n                    {generatePreview().systemPrompt && (\n                      <div>\n                        <Label>System Prompt</Label>\n                        <div className=\"mt-1 p-3 bg-muted rounded border font-mono text-sm whitespace-pre-wrap\">\n                          {generatePreview().systemPrompt}\n                        </div>\n                      </div>\n                    )}\n                    <div>\n                      <Label>User Prompt</Label>\n                      <div className=\"mt-1 p-3 bg-muted rounded border font-mono text-sm whitespace-pre-wrap\">\n                        {generatePreview().userPrompt}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AArBA;;;;;;;;;;;AA+BO,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAqB;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,gBAAgB;gBAAE,GAAG,MAAM;YAAC;YAC5B,0CAA0C;YAC1C,MAAM,gBAAqC,CAAC;YAC5C,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;gBAC/B,IAAI,SAAS,YAAY,KAAK,WAAW;oBACvC,aAAa,CAAC,SAAS,IAAI,CAAC,GAAG,SAAS,YAAY;gBACtD;YACF;YACA,iBAAiB;QACnB;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,UAAU,CAAC,cAAc,OAAO;IAErC,MAAM,aAAa;QACjB,MAAM,aAAa,qHAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QACzC,IAAI,WAAW,KAAK,EAAE;YACpB,OAAO;YACP;QACF,OAAO;YACL,oBAAoB,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;QAC1E;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,gBAAgB,CAAA,OAAQ,OAAO;gBAC7B,GAAG,IAAI;gBACP,UAAU;oBACR,GAAG,KAAK,QAAQ;oBAChB,CAAC,MAAM,EAAE;oBACT,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF,IAAI;IACN;IAEA,MAAM,sBAAsB,CAAC,OAAe;QAC1C,gBAAgB,CAAA,OAAQ,OAAO;gBAC7B,GAAG,IAAI;gBACP,SAAS;oBACP,GAAG,KAAK,OAAO;oBACf,CAAC,MAAM,EAAE;gBACX;YACF,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,cAA+B,YAAY;YAC/C,MAAM;YACN,MAAM;YACN,aAAa;YACb,UAAU;QACZ;QAEA,gBAAgB,CAAA,OAAQ,OAAO;gBAC7B,GAAG,IAAI;gBACP,SAAS;oBACP,GAAG,KAAK,OAAO;oBACf,WAAW;2BAAI,KAAK,OAAO,CAAC,SAAS;wBAAE;qBAAY;gBACrD;YACF,IAAI;IACN;IAEA,MAAM,iBAAiB,CAAC,OAAe,OAAe;QACpD,gBAAgB,CAAA,OAAQ,OAAO;gBAC7B,GAAG,IAAI;gBACP,SAAS;oBACP,GAAG,KAAK,OAAO;oBACf,WAAW,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,IAC/C,MAAM,QAAQ;4BAAE,GAAG,QAAQ;4BAAE,CAAC,MAAM,EAAE;wBAAM,IAAI;gBAEpD;YACF,IAAI;IACN;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB,CAAA,OAAQ,OAAO;gBAC7B,GAAG,IAAI;gBACP,SAAS;oBACP,GAAG,KAAK,OAAO;oBACf,WAAW,KAAK,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC3D;YACF,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc,OAAO;YAAE,YAAY;YAAI,cAAc;QAAG;QAC7D,OAAO,qHAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,cAAc;IAC7C;IAEA,MAAM,qBAAqB,qHAAA,CAAA,eAAY,CAAC,OAAO,CAAC,aAAa,OAAO,CAAC,UAAU,GAAG,CAAC,aAAa,OAAO,CAAC,YAAY,IAAI,EAAE;IAE1H,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,kBAAe;;wCACb,aAAa,QAAQ,CAAC,IAAI;wCAAC;wCAAI,aAAa,QAAQ,CAAC,IAAI;wCAAC;;;;;;;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;gCACZ,2BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS,IAAM,UAAU;;sDACzC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,SAAS;wCAC3C,MAAM,UAAU,qHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;wCACvC,MAAM,OAAO,IAAI,KAAK;4CAAC;yCAAQ,EAAE;4CAAE,MAAM;wCAAmB;wCAC5D,MAAM,MAAM,IAAI,eAAe,CAAC;wCAChC,MAAM,IAAI,SAAS,aAAa,CAAC;wCACjC,EAAE,IAAI,GAAG;wCACT,EAAE,QAAQ,GAAG,GAAG,aAAa,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,QAAQ,CAAC;wCAClF,EAAE,KAAK;wCACP,IAAI,eAAe,CAAC;oCACtB;;sDACE,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;;sDACzB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAQ,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,iBAAiB,MAAM,GAAG,mBACzB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;sDAAiB;2CAAR;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;;8CACrC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKpC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,OAAO,CAAC,YAAY,IAAI;wDAC5C,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDACnE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,OAAO,CAAC,UAAU;wDACtC,UAAU,CAAC,IAAM,oBAAoB,cAAc,EAAE,MAAM,CAAC,KAAK;wDACjE,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;;4DAAqC;4DAC7B,mBAAmB,MAAM,GAAG,IAAI,mBAAmB,IAAI,CAAC,QAAQ;;;;;;;;;;;;;0DAIzF,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,OAAO,CAAC,eAAe,IAAI;wDAC/C,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACtE,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;;sDACvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,UAAU,CAAC;gEACT,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,uHAAA,CAAA,mBAAgB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;oEACtD,YAAY,uHAAA,CAAA,mBAAgB,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC;oEAC5C,EAAE,MAAM,CAAC,KAAK,GAAG;gEACnB;4DACF;4DACA,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,OAAO,IAAI,CAAC,uHAAA,CAAA,mBAAgB,EAAE,GAAG,CAAC,CAAA,oBACjC,8OAAC;wEAAiB,OAAO;kFAAM;uEAAlB;;;;;;;;;;;sEAGjB,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAS,IAAM;;8EAC/B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAMvC,8OAAC;4CAAI,WAAU;sDACZ,aAAa,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC7C,8OAAC,gIAAA,CAAA,OAAI;oDAAa,WAAU;8DAC1B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,eAAe,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC7D,aAAY;;;;;;;;;;;;0EAGhB,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEACC,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,eAAe,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC7D,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAO;;;;;;0FACrB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;0FACvB,8OAAC;gFAAO,OAAM;0FAAU;;;;;;0FACxB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;0FACvB,8OAAC;gFAAO,OAAM;0FAAc;;;;;;;;;;;;;;;;;;0EAGhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,SAAS,WAAW,IAAI;wEAC/B,UAAU,CAAC,IAAM,eAAe,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;wEACpE,aAAY;;;;;;;;;;;;4DAGf,CAAC,SAAS,IAAI,KAAK,YAAY,SAAS,IAAI,KAAK,aAAa,mBAC7D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,SAAS,OAAO,EAAE,KAAK,SAAS;wEACvC,UAAU,CAAC,IAAM,eAAe,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC;wEACtG,aAAY;;;;;;;;;;;;0EAIlB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAM,WAAU;;8FACf,8OAAC;oFACC,MAAK;oFACL,SAAS,SAAS,QAAQ,IAAI;oFAC9B,UAAU,CAAC,IAAM,eAAe,OAAO,YAAY,EAAE,MAAM,CAAC,OAAO;;;;;;gFACnE;;;;;;;;;;;;kFAIN,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS,IAAM,eAAe;wEAC9B,WAAU;kFAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDA3Df;;;;;;;;;;;;;;;;8CAoEjB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,IAAI;wDACjC,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGhE,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,OAAO;wDACpC,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC/D,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,WAAW;wDACxC,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;wDACnE,WAAU;;;;;;;;;;;;0DAGd,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,QAAQ;wDACrC,UAAU,CAAC,IAAM,qBAAqB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAChE,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAc;;;;;;0EAC5B,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAW;;;;;;;;;;;;;;;;;;0DAG7B,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,8OAAC;wDACC,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,eAAe;wDAC5C,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACvE,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;;;;;;;;;;;;;0DAGzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,aAAa,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;wDACvC,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC;wDAClG,aAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEACZ,aAAa,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,yBACnC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;;4EAAE,SAAS,IAAI;4EAAC;4EAAE,SAAS,QAAQ,IAAI;;;;;;;oEAC5C,SAAS,IAAI,KAAK,yBACjB,8OAAC;wEACC,OAAO,aAAa,CAAC,SAAS,IAAI,CAAC,IAAI;wEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEACvF,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAG;;;;;;4EAChB,SAAS,OAAO,EAAE,IAAI,CAAA,uBACrB,8OAAC;oFAAoB,OAAO;8FAAS;mFAAxB;;;;;;;;;;+EAGf,SAAS,IAAI,KAAK,0BACpB,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAM,WAAU;;8FACf,8OAAC;oFACC,MAAK;oFACL,SAAS,aAAa,CAAC,SAAS,IAAI,CAAC,IAAI;oFACzC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;gGAAE,GAAG,IAAI;gGAAE,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;4FAAC,CAAC;;;;;;gFAE1F,SAAS,WAAW,IAAI,SAAS,IAAI;;;;;;;;;;;6FAI1C,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAM,SAAS,IAAI,KAAK,WAAW,WAAW;wEAC9C,OAAO,aAAa,CAAC,SAAS,IAAI,CAAC,IAAI;wEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEACvF,aAAa,SAAS,WAAW,IAAI,SAAS,WAAW;wEACzD,WAAU;;;;;;;+DA9BN,SAAS,IAAI;;;;;;;;;;;;;;;;0DAsC7B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;4DACZ,kBAAkB,YAAY,kBAC7B,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAI,WAAU;kFACZ,kBAAkB,YAAY;;;;;;;;;;;;0EAIrC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAI,WAAU;kFACZ,kBAAkB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrD", "debugId": null}}, {"offset": {"line": 3821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/data/sample-modules.ts"], "sourcesContent": ["// Sample .promptx modules for Catalyst\n\nimport { PromptxModule } from '@/types/promptx';\n\nexport const SAMPLE_MODULES: PromptxModule[] = [\n  {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"Content Writer\",\n      description: \"Professional content writing assistant for articles, blogs, and marketing copy\",\n      version: \"1.2.0\",\n      author: \"Catalyst Team\",\n      tags: [\"writing\", \"content\", \"marketing\", \"blog\"],\n      category: \"core\",\n      modelAffinity: [\"gpt-4o\", \"claude-3.5-sonnet\"],\n      formattingStyle: \"markdown\",\n      type: \"single\",\n      createdAt: \"2024-01-15T10:00:00Z\",\n      updatedAt: \"2024-01-20T14:30:00Z\"\n    },\n    content: {\n      systemPrompt: \"You are an expert content writer with years of experience in creating engaging, well-structured content across various industries. You understand SEO principles, audience engagement, and brand voice consistency.\",\n      userPrompt: \"Write a {{content_type}} about {{topic}} for {{audience}}.\\n\\nTone: {{tone}}\\nLength: {{length}}\\nFormat: {{format}}\\n\\nAdditional requirements:\\n{{requirements}}\",\n      variables: [\n        {\n          name: \"content_type\",\n          type: \"select\",\n          description: \"Type of content to create\",\n          required: true,\n          options: [\"blog post\", \"article\", \"social media post\", \"email newsletter\", \"product description\", \"landing page copy\"],\n          defaultValue: \"blog post\"\n        },\n        {\n          name: \"topic\",\n          type: \"text\",\n          description: \"Main topic or subject\",\n          required: true,\n          placeholder: \"e.g., sustainable technology trends\"\n        },\n        {\n          name: \"audience\",\n          type: \"text\",\n          description: \"Target audience\",\n          required: true,\n          placeholder: \"e.g., tech professionals, small business owners\"\n        },\n        {\n          name: \"tone\",\n          type: \"select\",\n          description: \"Writing tone and style\",\n          required: false,\n          options: [\"professional\", \"casual\", \"friendly\", \"authoritative\", \"conversational\", \"technical\"],\n          defaultValue: \"professional\"\n        },\n        {\n          name: \"length\",\n          type: \"select\",\n          description: \"Content length\",\n          required: false,\n          options: [\"short (300-500 words)\", \"medium (500-1000 words)\", \"long (1000-2000 words)\", \"comprehensive (2000+ words)\"],\n          defaultValue: \"medium (500-1000 words)\"\n        },\n        {\n          name: \"format\",\n          type: \"select\",\n          description: \"Output format preference\",\n          required: false,\n          options: [\"markdown with headers\", \"plain text\", \"HTML structure\", \"bullet points\", \"numbered sections\"],\n          defaultValue: \"markdown with headers\"\n        },\n        {\n          name: \"requirements\",\n          type: \"text\",\n          description: \"Any specific requirements or guidelines\",\n          required: false,\n          placeholder: \"e.g., include statistics, mention specific products, SEO keywords\"\n        }\n      ],\n      contextRetention: true\n    }\n  },\n  {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"Code Analyzer\",\n      description: \"Analyzes code for bugs, performance issues, and best practices\",\n      version: \"1.1.0\",\n      author: \"Catalyst Team\",\n      tags: [\"coding\", \"debugging\", \"analysis\", \"review\"],\n      category: \"specialized\",\n      modelAffinity: [\"gpt-4o\", \"claude-3.5-sonnet\"],\n      formattingStyle: \"markdown\",\n      type: \"single\",\n      createdAt: \"2024-01-10T09:00:00Z\",\n      updatedAt: \"2024-01-18T16:45:00Z\"\n    },\n    content: {\n      systemPrompt: \"You are a senior software engineer and code reviewer with expertise across multiple programming languages. You excel at identifying bugs, security vulnerabilities, performance issues, and suggesting improvements following best practices.\",\n      userPrompt: \"Analyze the following {{language}} code for:\\n- Bugs and potential issues\\n- Performance optimizations\\n- Security vulnerabilities\\n- Code quality and best practices\\n- {{focus_area}}\\n\\n```{{language}}\\n{{code}}\\n```\\n\\nProvide specific recommendations with examples where possible.\",\n      variables: [\n        {\n          name: \"language\",\n          type: \"select\",\n          description: \"Programming language\",\n          required: true,\n          options: [\"JavaScript\", \"TypeScript\", \"Python\", \"Java\", \"C#\", \"Go\", \"Rust\", \"PHP\", \"Ruby\", \"C++\"],\n          defaultValue: \"JavaScript\"\n        },\n        {\n          name: \"code\",\n          type: \"text\",\n          description: \"Code to analyze\",\n          required: true,\n          placeholder: \"Paste your code here...\"\n        },\n        {\n          name: \"focus_area\",\n          type: \"select\",\n          description: \"Specific area to focus on\",\n          required: false,\n          options: [\"General review\", \"Performance optimization\", \"Security audit\", \"Refactoring suggestions\", \"Testing recommendations\", \"Documentation review\"],\n          defaultValue: \"General review\"\n        }\n      ],\n      contextRetention: false\n    }\n  },\n  {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"Meeting Summarizer\",\n      description: \"Creates structured summaries of meetings with action items and key decisions\",\n      version: \"1.0.0\",\n      author: \"Catalyst Team\",\n      tags: [\"meetings\", \"summary\", \"productivity\", \"business\"],\n      category: \"specialized\",\n      modelAffinity: [\"gpt-4o\", \"claude-3.5-sonnet\"],\n      formattingStyle: \"markdown\",\n      type: \"single\",\n      createdAt: \"2024-01-12T11:30:00Z\",\n      updatedAt: \"2024-01-12T11:30:00Z\"\n    },\n    content: {\n      systemPrompt: \"You are an executive assistant skilled at creating clear, actionable meeting summaries. You excel at identifying key decisions, action items, and important discussion points from meeting transcripts or notes.\",\n      userPrompt: \"Create a structured summary of this {{meeting_type}} meeting:\\n\\n{{meeting_content}}\\n\\nInclude:\\n- Meeting overview\\n- Key decisions made\\n- Action items with owners and deadlines\\n- Important discussion points\\n- Next steps\\n\\nFormat: {{output_format}}\",\n      variables: [\n        {\n          name: \"meeting_type\",\n          type: \"select\",\n          description: \"Type of meeting\",\n          required: true,\n          options: [\"team standup\", \"project review\", \"client meeting\", \"board meeting\", \"brainstorming session\", \"one-on-one\", \"all-hands\"],\n          defaultValue: \"team standup\"\n        },\n        {\n          name: \"meeting_content\",\n          type: \"text\",\n          description: \"Meeting transcript, notes, or recording\",\n          required: true,\n          placeholder: \"Paste meeting transcript or detailed notes here...\"\n        },\n        {\n          name: \"output_format\",\n          type: \"select\",\n          description: \"Summary format\",\n          required: false,\n          options: [\"structured markdown\", \"bullet points\", \"executive summary\", \"detailed report\"],\n          defaultValue: \"structured markdown\"\n        }\n      ],\n      contextRetention: false\n    }\n  },\n  {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"Research Assistant\",\n      description: \"Comprehensive research and analysis on any topic with citations and insights\",\n      version: \"1.3.0\",\n      author: \"Catalyst Team\",\n      tags: [\"research\", \"analysis\", \"academic\", \"insights\"],\n      category: \"core\",\n      modelAffinity: [\"gpt-4o\", \"claude-3.5-sonnet\"],\n      formattingStyle: \"markdown\",\n      type: \"single\",\n      createdAt: \"2024-01-08T14:20:00Z\",\n      updatedAt: \"2024-01-22T09:15:00Z\"\n    },\n    content: {\n      systemPrompt: \"You are a research specialist with expertise in gathering, analyzing, and synthesizing information from multiple sources. You provide well-structured research with proper citations and actionable insights.\",\n      userPrompt: \"Research {{topic}} with focus on {{research_focus}}.\\n\\nScope: {{scope}}\\nDepth: {{depth}}\\nAudience: {{audience}}\\n\\nProvide:\\n1. Executive summary\\n2. Key findings\\n3. Detailed analysis\\n4. Recommendations\\n5. Sources and further reading\\n\\nSpecial requirements: {{requirements}}\",\n      variables: [\n        {\n          name: \"topic\",\n          type: \"text\",\n          description: \"Research topic\",\n          required: true,\n          placeholder: \"e.g., impact of AI on healthcare industry\"\n        },\n        {\n          name: \"research_focus\",\n          type: \"select\",\n          description: \"Primary research focus\",\n          required: true,\n          options: [\"market trends\", \"competitive analysis\", \"technology assessment\", \"policy implications\", \"financial impact\", \"social implications\", \"future predictions\"],\n          defaultValue: \"market trends\"\n        },\n        {\n          name: \"scope\",\n          type: \"select\",\n          description: \"Research scope\",\n          required: false,\n          options: [\"global\", \"regional\", \"national\", \"industry-specific\", \"company-specific\"],\n          defaultValue: \"industry-specific\"\n        },\n        {\n          name: \"depth\",\n          type: \"select\",\n          description: \"Research depth\",\n          required: false,\n          options: [\"overview\", \"detailed\", \"comprehensive\", \"expert-level\"],\n          defaultValue: \"detailed\"\n        },\n        {\n          name: \"audience\",\n          type: \"text\",\n          description: \"Target audience\",\n          required: false,\n          placeholder: \"e.g., executives, investors, technical team\",\n          defaultValue: \"business professionals\"\n        },\n        {\n          name: \"requirements\",\n          type: \"text\",\n          description: \"Special requirements or constraints\",\n          required: false,\n          placeholder: \"e.g., focus on recent developments, include financial data\"\n        }\n      ],\n      contextRetention: true\n    }\n  },\n  {\n    schema: \"1.0.0\",\n    metadata: {\n      name: \"Email Composer\",\n      description: \"Professional email writing for various business contexts\",\n      version: \"1.1.0\",\n      author: \"Catalyst Team\",\n      tags: [\"email\", \"communication\", \"business\", \"professional\"],\n      category: \"core\",\n      modelAffinity: [\"gpt-4o\", \"claude-3.5-sonnet\"],\n      formattingStyle: \"plaintext\",\n      type: \"single\",\n      createdAt: \"2024-01-14T08:45:00Z\",\n      updatedAt: \"2024-01-19T13:20:00Z\"\n    },\n    content: {\n      systemPrompt: \"You are a professional communication expert who writes clear, effective emails that achieve their intended purpose while maintaining appropriate tone and professionalism.\",\n      userPrompt: \"Write a {{email_type}} email:\\n\\nTo: {{recipient}}\\nSubject: {{subject}}\\nPurpose: {{purpose}}\\nTone: {{tone}}\\nKey points to include:\\n{{key_points}}\\n\\nContext: {{context}}\",\n      variables: [\n        {\n          name: \"email_type\",\n          type: \"select\",\n          description: \"Type of email\",\n          required: true,\n          options: [\"business inquiry\", \"follow-up\", \"meeting request\", \"proposal\", \"complaint\", \"thank you\", \"introduction\", \"update\", \"request for information\"],\n          defaultValue: \"business inquiry\"\n        },\n        {\n          name: \"recipient\",\n          type: \"text\",\n          description: \"Email recipient\",\n          required: true,\n          placeholder: \"e.g., John Smith, CEO of TechCorp\"\n        },\n        {\n          name: \"subject\",\n          type: \"text\",\n          description: \"Email subject (optional - will be suggested if empty)\",\n          required: false,\n          placeholder: \"Leave empty for AI-generated subject\"\n        },\n        {\n          name: \"purpose\",\n          type: \"text\",\n          description: \"Main purpose of the email\",\n          required: true,\n          placeholder: \"e.g., schedule a demo meeting, request partnership information\"\n        },\n        {\n          name: \"tone\",\n          type: \"select\",\n          description: \"Email tone\",\n          required: false,\n          options: [\"formal\", \"professional\", \"friendly\", \"casual\", \"urgent\", \"diplomatic\"],\n          defaultValue: \"professional\"\n        },\n        {\n          name: \"key_points\",\n          type: \"text\",\n          description: \"Key points to include\",\n          required: true,\n          placeholder: \"List the main points you want to communicate\"\n        },\n        {\n          name: \"context\",\n          type: \"text\",\n          description: \"Additional context or background\",\n          required: false,\n          placeholder: \"Any relevant background information\"\n        }\n      ],\n      contextRetention: false\n    }\n  }\n];\n\n// Helper function to get modules by category\nexport function getModulesByCategory(category: string): PromptxModule[] {\n  return SAMPLE_MODULES.filter(module => module.metadata.category === category);\n}\n\n// Helper function to get modules by tag\nexport function getModulesByTag(tag: string): PromptxModule[] {\n  return SAMPLE_MODULES.filter(module => module.metadata.tags.includes(tag));\n}\n\n// Helper function to search modules\nexport function searchModules(query: string): PromptxModule[] {\n  const lowercaseQuery = query.toLowerCase();\n  return SAMPLE_MODULES.filter(module => \n    module.metadata.name.toLowerCase().includes(lowercaseQuery) ||\n    module.metadata.description.toLowerCase().includes(lowercaseQuery) ||\n    module.metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;AAIhC,MAAM,iBAAkC;IAC7C;QACE,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;gBAAC;gBAAW;gBAAW;gBAAa;aAAO;YACjD,UAAU;YACV,eAAe;gBAAC;gBAAU;aAAoB;YAC9C,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,cAAc;YACd,YAAY;YACZ,WAAW;gBACT;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAa;wBAAW;wBAAqB;wBAAoB;wBAAuB;qBAAoB;oBACtH,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAgB;wBAAU;wBAAY;wBAAiB;wBAAkB;qBAAY;oBAC/F,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAyB;wBAA2B;wBAA0B;qBAA8B;oBACtH,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAyB;wBAAc;wBAAkB;wBAAiB;qBAAoB;oBACxG,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;aACD;YACD,kBAAkB;QACpB;IACF;IACA;QACE,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;gBAAC;gBAAU;gBAAa;gBAAY;aAAS;YACnD,UAAU;YACV,eAAe;gBAAC;gBAAU;aAAoB;YAC9C,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,cAAc;YACd,YAAY;YACZ,WAAW;gBACT;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAc;wBAAc;wBAAU;wBAAQ;wBAAM;wBAAM;wBAAQ;wBAAO;wBAAQ;qBAAM;oBACjG,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAkB;wBAA4B;wBAAkB;wBAA2B;wBAA2B;qBAAuB;oBACvJ,cAAc;gBAChB;aACD;YACD,kBAAkB;QACpB;IACF;IACA;QACE,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;gBAAC;gBAAY;gBAAW;gBAAgB;aAAW;YACzD,UAAU;YACV,eAAe;gBAAC;gBAAU;aAAoB;YAC9C,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,cAAc;YACd,YAAY;YACZ,WAAW;gBACT;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAgB;wBAAkB;wBAAkB;wBAAiB;wBAAyB;wBAAc;qBAAY;oBAClI,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAuB;wBAAiB;wBAAqB;qBAAkB;oBACzF,cAAc;gBAChB;aACD;YACD,kBAAkB;QACpB;IACF;IACA;QACE,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;gBAAC;gBAAY;gBAAY;gBAAY;aAAW;YACtD,UAAU;YACV,eAAe;gBAAC;gBAAU;aAAoB;YAC9C,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,cAAc;YACd,YAAY;YACZ,WAAW;gBACT;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAiB;wBAAwB;wBAAyB;wBAAuB;wBAAoB;wBAAuB;qBAAqB;oBACnK,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAU;wBAAY;wBAAY;wBAAqB;qBAAmB;oBACpF,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAY;wBAAY;wBAAiB;qBAAe;oBAClE,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;oBACb,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;aACD;YACD,kBAAkB;QACpB;IACF;IACA;QACE,QAAQ;QACR,UAAU;YACR,MAAM;YACN,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;gBAAC;gBAAS;gBAAiB;gBAAY;aAAe;YAC5D,UAAU;YACV,eAAe;gBAAC;gBAAU;aAAoB;YAC9C,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,cAAc;YACd,YAAY;YACZ,WAAW;gBACT;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAoB;wBAAa;wBAAmB;wBAAY;wBAAa;wBAAa;wBAAgB;wBAAU;qBAA0B;oBACxJ,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,SAAS;wBAAC;wBAAU;wBAAgB;wBAAY;wBAAU;wBAAU;qBAAa;oBACjF,cAAc;gBAChB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,aAAa;gBACf;aACD;YACD,kBAAkB;QACpB;IACF;CACD;AAGM,SAAS,qBAAqB,QAAgB;IACnD,OAAO,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC,QAAQ,KAAK;AACtE;AAGO,SAAS,gBAAgB,GAAW;IACzC,OAAO,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvE;AAGO,SAAS,cAAc,KAAa;IACzC,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,eAAe,MAAM,CAAC,CAAA,SAC3B,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC5C,OAAO,QAAQ,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACnD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;AAEhE", "debugId": null}}, {"offset": {"line": 4289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/workspace/module-workspace.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from 'react';\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  FileText, \n  Plus, \n  Upload, \n  Settings, \n  Play,\n  Edit,\n  Library,\n  Zap\n} from 'lucide-react';\nimport { ModuleManager } from '@/components/modules/module-manager';\nimport { ModuleEditor } from '@/components/modules/module-editor';\nimport { PromptxModule } from '@/types/promptx';\nimport { SAMPLE_MODULES } from '@/data/sample-modules';\nimport { PromptxUtils } from '@/lib/promptx';\n\nexport function ModuleWorkspace() {\n  const [modules, setModules] = useState<PromptxModule[]>(SAMPLE_MODULES);\n  const [selectedModule, setSelectedModule] = useState<PromptxModule | null>(null);\n  const [isEditorOpen, setIsEditorOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('library');\n\n  const handleModuleLoad = (module: PromptxModule) => {\n    // Check if module already exists (by name and creation date)\n    const existingIndex = modules.findIndex(m => \n      m.metadata.name === module.metadata.name && \n      m.metadata.createdAt === module.metadata.createdAt\n    );\n    \n    if (existingIndex >= 0) {\n      // Update existing module\n      const updatedModules = [...modules];\n      updatedModules[existingIndex] = module;\n      setModules(updatedModules);\n    } else {\n      // Add new module\n      setModules(prev => [...prev, module]);\n    }\n  };\n\n  const handleModuleCreate = (template: string) => {\n    let newModule: PromptxModule;\n    \n    switch (template) {\n      case 'writer':\n        newModule = PromptxUtils.createFromTemplate('New Writer Module', 'writer');\n        break;\n      case 'analyzer':\n        newModule = PromptxUtils.createFromTemplate('New Analyzer Module', 'analyzer');\n        break;\n      case 'coder':\n        newModule = PromptxUtils.createFromTemplate('New Coder Module', 'coder');\n        break;\n      case 'qa':\n        newModule = PromptxUtils.createFromTemplate('New Q&A Module', 'qa');\n        break;\n      default:\n        newModule = PromptxUtils.createEmpty();\n        break;\n    }\n    \n    setSelectedModule(newModule);\n    setIsEditorOpen(true);\n  };\n\n  const handleModuleEdit = (module: PromptxModule) => {\n    setSelectedModule(module);\n    setIsEditorOpen(true);\n  };\n\n  const handleModuleDelete = (moduleId: string) => {\n    setModules(prev => prev.filter(m => `${m.metadata.name}-${m.metadata.createdAt}` !== moduleId));\n  };\n\n  const handleModuleExecute = (module: PromptxModule) => {\n    // For now, just open the editor in preview mode\n    setSelectedModule(module);\n    setIsEditorOpen(true);\n  };\n\n  const handleModuleSave = (module: PromptxModule) => {\n    handleModuleLoad(module);\n  };\n\n  const getQuickStartModules = () => {\n    return modules.filter(m => m.metadata.category === 'core').slice(0, 4);\n  };\n\n  const getEngineModules = () => {\n    return modules.filter(m => m.metadata.category === 'engine');\n  };\n\n  const getRecentModules = () => {\n    return [...modules]\n      .sort((a, b) => new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime())\n      .slice(0, 6);\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"flex-1 overflow-hidden\">\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"h-full flex flex-col\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"library\">\n              <Library className=\"w-4 h-4 mr-2\" />\n              Module Library\n            </TabsTrigger>\n            <TabsTrigger value=\"engines\">\n              <Zap className=\"w-4 h-4 mr-2\" />\n              Prompt Engines\n            </TabsTrigger>\n            <TabsTrigger value=\"recent\">\n              <FileText className=\"w-4 h-4 mr-2\" />\n              Recent\n            </TabsTrigger>\n            <TabsTrigger value=\"create\">\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Create\n            </TabsTrigger>\n          </TabsList>\n\n          <div className=\"flex-1 overflow-auto p-6\">\n            <TabsContent value=\"library\" className=\"mt-0 h-full\">\n              <ModuleManager\n                modules={modules}\n                onModuleLoad={handleModuleLoad}\n                onModuleCreate={handleModuleCreate}\n                onModuleEdit={handleModuleEdit}\n                onModuleDelete={handleModuleDelete}\n                onModuleExecute={handleModuleExecute}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"engines\" className=\"mt-0 space-y-6\">\n              <div>\n                <h2 className=\"text-2xl font-bold mb-4\">Prompt Engines</h2>\n                <p className=\"text-muted-foreground mb-6\">\n                  Pre-built chains of modules for complex workflows\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {getEngineModules().map((module) => (\n                  <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className=\"module-card group\">\n                    <CardHeader>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2\">\n                          <Zap className=\"w-5 h-5 text-catalyst-purple\" />\n                          <CardTitle className=\"text-lg\">{module.metadata.name}</CardTitle>\n                        </div>\n                        <Badge className=\"bg-purple-500/20 text-purple-400 border-purple-500/30\">\n                          Engine\n                        </Badge>\n                      </div>\n                      <CardDescription>{module.metadata.description}</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"flex flex-wrap gap-1 mb-4\">\n                        {module.metadata.tags.map((tag) => (\n                          <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                      <div className=\"flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <Button size=\"sm\" onClick={() => handleModuleExecute(module)}>\n                          <Play className=\"w-4 h-4 mr-2\" />\n                          Run\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\" onClick={() => handleModuleEdit(module)}>\n                          <Edit className=\"w-4 h-4 mr-2\" />\n                          Edit\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n\n              {getEngineModules().length === 0 && (\n                <Card className=\"text-center py-12\">\n                  <CardContent>\n                    <Zap className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\n                    <CardTitle className=\"text-lg mb-2\">No engines available</CardTitle>\n                    <CardDescription className=\"mb-4\">\n                      Create your first prompt engine to automate complex workflows\n                    </CardDescription>\n                    <Button onClick={() => handleModuleCreate('custom')}>\n                      <Plus className=\"w-4 h-4 mr-2\" />\n                      Create Engine\n                    </Button>\n                  </CardContent>\n                </Card>\n              )}\n            </TabsContent>\n\n            <TabsContent value=\"recent\" className=\"mt-0 space-y-6\">\n              <div>\n                <h2 className=\"text-2xl font-bold mb-4\">Recent Modules</h2>\n                <p className=\"text-muted-foreground mb-6\">\n                  Recently created or modified modules\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {getRecentModules().map((module) => (\n                  <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className=\"module-card group\">\n                    <CardHeader>\n                      <div className=\"flex items-center justify-between\">\n                        <CardTitle className=\"text-lg\">{module.metadata.name}</CardTitle>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {new Date(module.metadata.updatedAt).toLocaleDateString()}\n                        </Badge>\n                      </div>\n                      <CardDescription>{module.metadata.description}</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"flex flex-wrap gap-1 mb-4\">\n                        {module.metadata.tags.slice(0, 3).map((tag) => (\n                          <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                            {tag}\n                          </Badge>\n                        ))}\n                      </div>\n                      <div className=\"flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <Button size=\"sm\" onClick={() => handleModuleExecute(module)}>\n                          <Play className=\"w-4 h-4 mr-2\" />\n                          Use\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline\" onClick={() => handleModuleEdit(module)}>\n                          <Edit className=\"w-4 h-4 mr-2\" />\n                          Edit\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"create\" className=\"mt-0 space-y-6\">\n              <div>\n                <h2 className=\"text-2xl font-bold mb-4\">Create New Module</h2>\n                <p className=\"text-muted-foreground mb-6\">\n                  Start with a template or create from scratch\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                <Card className=\"cursor-pointer hover:shadow-lg transition-shadow catalyst-border\" onClick={() => handleModuleCreate('writer')}>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <FileText className=\"w-5 h-5\" />\n                      Content Writer\n                    </CardTitle>\n                    <CardDescription>\n                      Create articles, blogs, and marketing copy\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex flex-wrap gap-1\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">writing</Badge>\n                      <Badge variant=\"secondary\" className=\"text-xs\">content</Badge>\n                      <Badge variant=\"secondary\" className=\"text-xs\">marketing</Badge>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"cursor-pointer hover:shadow-lg transition-shadow catalyst-border\" onClick={() => handleModuleCreate('analyzer')}>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Settings className=\"w-5 h-5\" />\n                      Content Analyzer\n                    </CardTitle>\n                    <CardDescription>\n                      Analyze and provide insights on content\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex flex-wrap gap-1\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">analysis</Badge>\n                      <Badge variant=\"secondary\" className=\"text-xs\">insights</Badge>\n                      <Badge variant=\"secondary\" className=\"text-xs\">review</Badge>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"cursor-pointer hover:shadow-lg transition-shadow catalyst-border\" onClick={() => handleModuleCreate('custom')}>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Plus className=\"w-5 h-5\" />\n                      Custom Module\n                    </CardTitle>\n                    <CardDescription>\n                      Start from scratch with a blank module\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex flex-wrap gap-1\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">custom</Badge>\n                      <Badge variant=\"secondary\" className=\"text-xs\">blank</Badge>\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n                <div className=\"flex gap-4\">\n                  <Button variant=\"outline\" onClick={() => document.getElementById('file-import')?.click()}>\n                    <Upload className=\"w-4 h-4 mr-2\" />\n                    Import .promptx File\n                  </Button>\n                  <input\n                    id=\"file-import\"\n                    type=\"file\"\n                    accept=\".promptx,.json\"\n                    className=\"hidden\"\n                    onChange={(e) => {\n                      const file = e.target.files?.[0];\n                      if (file) {\n                        const reader = new FileReader();\n                        reader.onload = (event) => {\n                          const content = event.target?.result as string;\n                          const { module } = PromptxUtils.parse(content);\n                          if (module) {\n                            handleModuleLoad(module);\n                            setActiveTab('library');\n                          }\n                        };\n                        reader.readAsText(file);\n                      }\n                    }}\n                  />\n                </div>\n              </div>\n            </TabsContent>\n          </div>\n        </Tabs>\n      </div>\n\n      <ModuleEditor\n        module={selectedModule}\n        isOpen={isEditorOpen}\n        onClose={() => {\n          setIsEditorOpen(false);\n          setSelectedModule(null);\n        }}\n        onSave={handleModuleSave}\n        onExecute={handleModuleExecute}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAEA;AACA;AArBA;;;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,gIAAA,CAAA,iBAAc;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,CAAC;QACxB,6DAA6D;QAC7D,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAA,IACtC,EAAE,QAAQ,CAAC,IAAI,KAAK,OAAO,QAAQ,CAAC,IAAI,IACxC,EAAE,QAAQ,CAAC,SAAS,KAAK,OAAO,QAAQ,CAAC,SAAS;QAGpD,IAAI,iBAAiB,GAAG;YACtB,yBAAyB;YACzB,MAAM,iBAAiB;mBAAI;aAAQ;YACnC,cAAc,CAAC,cAAc,GAAG;YAChC,WAAW;QACb,OAAO;YACL,iBAAiB;YACjB,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,YAAY,qHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,qBAAqB;gBACjE;YACF,KAAK;gBACH,YAAY,qHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,uBAAuB;gBACnE;YACF,KAAK;gBACH,YAAY,qHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,oBAAoB;gBAChE;YACF,KAAK;gBACH,YAAY,qHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,kBAAkB;gBAC9D;YACF;gBACE,YAAY,qHAAA,CAAA,eAAY,CAAC,WAAW;gBACpC;QACJ;QAEA,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,SAAS,EAAE,KAAK;IACvF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gDAAgD;QAChD,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,KAAK,QAAQ,KAAK,CAAC,GAAG;IACtE;IAEA,MAAM,mBAAmB;QACvB,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,KAAK;IACrD;IAEA,MAAM,mBAAmB;QACvB,OAAO;eAAI;SAAQ,CAChB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,IAChG,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;sDACjB,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGtC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;sDACjB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;sDACjB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;sDACjB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC,kJAAA,CAAA,gBAAa;wCACZ,SAAS;wCACT,cAAc;wCACd,gBAAgB;wCAChB,cAAc;wCACd,gBAAgB;wCAChB,iBAAiB;;;;;;;;;;;8CAIrB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAC,uBACvB,8OAAC,gIAAA,CAAA,OAAI;oDAA8D,WAAU;;sEAC3E,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;8FACf,8OAAC,gIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAW,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;sFAEtD,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwD;;;;;;;;;;;;8EAI3E,8OAAC,gIAAA,CAAA,kBAAe;8EAAE,OAAO,QAAQ,CAAC,WAAW;;;;;;;;;;;;sEAE/C,8OAAC,gIAAA,CAAA,cAAW;;8EACV,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,oBACzB,8OAAC,iIAAA,CAAA,QAAK;4EAAW,SAAQ;4EAAY,WAAU;sFAC5C;2EADS;;;;;;;;;;8EAKhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAS,IAAM,oBAAoB;;8FACnD,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,8OAAC,kIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;4EAAU,SAAS,IAAM,iBAAiB;;8FAClE,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;mDA3B9B,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE;;;;;;;;;;wCAoCpE,mBAAmB,MAAM,KAAK,mBAC7B,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAe;;;;;;kEACpC,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAO;;;;;;kEAGlC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAS,IAAM,mBAAmB;;0EACxC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAQ3C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAC,uBACvB,8OAAC,gIAAA,CAAA,OAAI;oDAA8D,WAAU;;sEAC3E,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAW,OAAO,QAAQ,CAAC,IAAI;;;;;;sFACpD,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,IAAI,KAAK,OAAO,QAAQ,CAAC,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8EAG3D,8OAAC,gIAAA,CAAA,kBAAe;8EAAE,OAAO,QAAQ,CAAC,WAAW;;;;;;;;;;;;sEAE/C,8OAAC,gIAAA,CAAA,cAAW;;8EACV,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrC,8OAAC,iIAAA,CAAA,QAAK;4EAAW,SAAQ;4EAAY,WAAU;sFAC5C;2EADS;;;;;;;;;;8EAKhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAS,IAAM,oBAAoB;;8FACnD,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,8OAAC,kIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;4EAAU,SAAS,IAAM,iBAAiB;;8FAClE,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;mDAxB9B,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,SAAS,EAAE;;;;;;;;;;;;;;;;8CAkCvE,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;oDAAmE,SAAS,IAAM,mBAAmB;;sEACnH,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGlC,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAInB,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;8DAKrD,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;oDAAmE,SAAS,IAAM,mBAAmB;;sEACnH,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAGlC,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAInB,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;8DAKrD,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;oDAAmE,SAAS,IAAM,mBAAmB;;sEACnH,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;;sFACnB,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAY;;;;;;;8EAG9B,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAInB,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAY,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,SAAS,IAAM,SAAS,cAAc,CAAC,gBAAgB;;8EAC/E,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGrC,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,QAAO;4DACP,WAAU;4DACV,UAAU,CAAC;gEACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gEAChC,IAAI,MAAM;oEACR,MAAM,SAAS,IAAI;oEACnB,OAAO,MAAM,GAAG,CAAC;wEACf,MAAM,UAAU,MAAM,MAAM,EAAE;wEAC9B,MAAM,EAAE,MAAM,EAAE,GAAG,qHAAA,CAAA,eAAY,CAAC,KAAK,CAAC;wEACtC,IAAI,QAAQ;4EACV,iBAAiB;4EACjB,aAAa;wEACf;oEACF;oEACA,OAAO,UAAU,CAAC;gEACpB;4DACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC,iJAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB;gBACpB;gBACA,QAAQ;gBACR,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 5294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/output/output-panel.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { \n  Zap, \n  RefreshCw, \n  Download, \n  Copy, \n  History,\n  Play,\n  Square,\n  MoreHorizontal\n} from \"lucide-react\";\n\ninterface SessionHistoryItem {\n  id: string;\n  title: string;\n  timestamp: string;\n  model: string;\n  tokens: number;\n  preview: string;\n}\n\nconst sessionHistory: SessionHistoryItem[] = [\n  {\n    id: \"1\",\n    title: \"Blog Post: AI in Healthcare\",\n    timestamp: \"2 minutes ago\",\n    model: \"GPT-4o\",\n    tokens: 1247,\n    preview: \"Artificial Intelligence is revolutionizing healthcare by enabling more accurate diagnoses...\"\n  },\n  {\n    id: \"2\", \n    title: \"Code Review: React Component\",\n    timestamp: \"15 minutes ago\",\n    model: \"Claude 3.5\",\n    tokens: 892,\n    preview: \"The component structure looks good overall. Here are some suggestions for improvement...\"\n  },\n  {\n    id: \"3\",\n    title: \"Interview Questions: Senior Dev\",\n    timestamp: \"1 hour ago\", \n    model: \"GPT-4o\",\n    tokens: 654,\n    preview: \"Here are comprehensive interview questions for a senior developer position...\"\n  }\n];\n\nexport function OutputPanel() {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [streamingText, setStreamingText] = useState(\"\");\n  const [activeOutputTab, setActiveOutputTab] = useState(\"live\");\n\n  const mockStreamingText = `# AI-Powered Healthcare Revolution\n\nArtificial Intelligence is fundamentally transforming the healthcare landscape, offering unprecedented opportunities to improve patient outcomes, reduce costs, and enhance the overall quality of care. This technological revolution is not just a distant future concept—it's happening now, with real-world applications already making significant impacts across various medical specialties.\n\n## Key Areas of Impact\n\n### 1. Diagnostic Accuracy\nAI systems are now capable of analyzing medical images with accuracy that often surpasses human specialists. From detecting early-stage cancers in radiology scans to identifying diabetic retinopathy in eye examinations, machine learning algorithms are becoming invaluable diagnostic tools.\n\n### 2. Personalized Treatment Plans\nBy analyzing vast amounts of patient data, including genetic information, medical history, and lifestyle factors, AI can help physicians develop highly personalized treatment strategies that are more effective and have fewer side effects.\n\n### 3. Drug Discovery and Development\nThe traditional drug discovery process, which typically takes 10-15 years and costs billions of dollars, is being accelerated through AI-powered molecular analysis and predictive modeling...`;\n\n  useEffect(() => {\n    if (isStreaming) {\n      const words = mockStreamingText.split(' ');\n      let currentIndex = 0;\n      \n      const interval = setInterval(() => {\n        if (currentIndex < words.length) {\n          setStreamingText(prev => prev + (currentIndex === 0 ? '' : ' ') + words[currentIndex]);\n          currentIndex++;\n        } else {\n          setIsStreaming(false);\n          clearInterval(interval);\n        }\n      }, 100);\n\n      return () => clearInterval(interval);\n    }\n  }, [isStreaming]);\n\n  const startStreaming = () => {\n    setStreamingText(\"\");\n    setIsStreaming(true);\n  };\n\n  const stopStreaming = () => {\n    setIsStreaming(false);\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"p-6 border-b border-border\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl font-semibold gradient-text\">AI Output Stream</h2>\n            <p className=\"text-sm text-muted-foreground\">Live results from your prompt chains</p>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button \n              variant=\"outline\" \n              size=\"sm\" \n              onClick={isStreaming ? stopStreaming : startStreaming}\n              className=\"gap-2\"\n            >\n              {isStreaming ? (\n                <>\n                  <Square className=\"w-4 h-4\" />\n                  Stop\n                </>\n              ) : (\n                <>\n                  <Play className=\"w-4 h-4\" />\n                  Run Chain\n                </>\n              )}\n            </Button>\n            \n            <Button variant=\"outline\" size=\"sm\" className=\"gap-2\">\n              <Download className=\"w-4 h-4\" />\n              Export\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex-1 overflow-hidden\">\n        <Tabs value={activeOutputTab} onValueChange={setActiveOutputTab} className=\"h-full flex flex-col\">\n          <div className=\"px-6 py-2 border-b border-border\">\n            <TabsList className=\"bg-muted/20\">\n              <TabsTrigger value=\"live\" className=\"flex items-center gap-2\">\n                <Zap className=\"w-4 h-4\" />\n                Live Output\n              </TabsTrigger>\n              <TabsTrigger value=\"history\" className=\"flex items-center gap-2\">\n                <History className=\"w-4 h-4\" />\n                Session History\n              </TabsTrigger>\n            </TabsList>\n          </div>\n\n          <div className=\"flex-1 overflow-hidden\">\n            <TabsContent value=\"live\" className=\"h-full m-0\">\n              <div className=\"h-full p-6\">\n                <Card className=\"h-full prompt-panel\">\n                  <div className=\"p-6 h-full flex flex-col\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <Badge variant=\"secondary\" className=\"gap-2\">\n                        <div className={`w-2 h-2 rounded-full ${isStreaming ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`}></div>\n                        {isStreaming ? 'Streaming Active' : 'Ready'}\n                      </Badge>\n                      <div className=\"flex items-center gap-2\">\n                        <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\n                          <Copy className=\"w-4 h-4\" />\n                          Copy\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\n                          <RefreshCw className=\"w-4 h-4\" />\n                          Regenerate\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <MoreHorizontal className=\"w-4 h-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                    \n                    <ScrollArea className=\"flex-1\">\n                      <div className=\"prose prose-invert max-w-none\">\n                        <div className=\"whitespace-pre-wrap text-sm leading-relaxed\">\n                          {streamingText || \"Click 'Run Chain' to start generating content...\"}\n                          {isStreaming && <span className=\"animate-pulse\">|</span>}\n                        </div>\n                      </div>\n                    </ScrollArea>\n\n                    {streamingText && (\n                      <div className=\"mt-4 pt-4 border-t border-border flex items-center justify-between text-xs text-muted-foreground\">\n                        <div className=\"flex items-center gap-4\">\n                          <span>Model: GPT-4o</span>\n                          <span>Tokens: {streamingText.split(' ').length * 1.3 | 0}</span>\n                          <span>Time: {isStreaming ? 'Generating...' : '2.3s'}</span>\n                        </div>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          Quality: High\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n                </Card>\n              </div>\n            </TabsContent>\n            \n            <TabsContent value=\"history\" className=\"h-full m-0\">\n              <div className=\"h-full p-6\">\n                <div className=\"space-y-3\">\n                  {sessionHistory.map((item) => (\n                    <Card key={item.id} className=\"module-card cursor-pointer\">\n                      <div className=\"p-4\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium text-sm\">{item.title}</h4>\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {item.model}\n                          </Badge>\n                        </div>\n                        <div className=\"flex items-center justify-between text-xs text-muted-foreground mb-3\">\n                          <span>{item.timestamp}</span>\n                          <span>{item.tokens} tokens</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                          {item.preview}\n                        </p>\n                        <div className=\"flex items-center gap-2 mt-3\">\n                          <Button variant=\"ghost\" size=\"sm\" className=\"text-xs h-7\">\n                            View Full\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\" className=\"text-xs h-7\">\n                            Export\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\" className=\"text-xs h-7\">\n                            Reuse\n                          </Button>\n                        </div>\n                      </div>\n                    </Card>\n                  ))}\n                </div>\n              </div>\n            </TabsContent>\n          </div>\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AA4BA,MAAM,iBAAuC;IAC3C;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAS;IACX;CACD;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;8LAaiK,CAAC;IAE7L,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM,QAAQ,kBAAkB,KAAK,CAAC;YACtC,IAAI,eAAe;YAEnB,MAAM,WAAW,YAAY;gBAC3B,IAAI,eAAe,MAAM,MAAM,EAAE;oBAC/B,iBAAiB,CAAA,OAAQ,OAAO,CAAC,iBAAiB,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,aAAa;oBACrF;gBACF,OAAO;oBACL,eAAe;oBACf,cAAc;gBAChB;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,cAAc,gBAAgB;oCACvC,WAAU;8CAET,4BACC;;0DACE,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;qEAIhC;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;8CAMlC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAiB,eAAe;oBAAoB,WAAU;;sCACzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAO,WAAU;;0DAClC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DACrC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;;kFACnC,8OAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,+BAA+B,eAAe;;;;;;oEACnG,cAAc,qBAAqB;;;;;;;0EAEtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;;0FAC1C,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAY;;;;;;;kFAG9B,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;;0FAC1C,8OAAC,gNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EAAY;;;;;;;kFAGnC,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKhC,8OAAC,0IAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,iBAAiB;oEACjB,6BAAe,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;oDAKrD,+BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAK;4EAAS,cAAc,KAAK,CAAC,KAAK,MAAM,GAAG,MAAM;;;;;;;kFACvD,8OAAC;;4EAAK;4EAAO,cAAc,kBAAkB;;;;;;;;;;;;;0EAE/C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUzD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,gIAAA,CAAA,OAAI;oDAAe,WAAU;8DAC5B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuB,KAAK,KAAK;;;;;;kFAC/C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,KAAK,KAAK;;;;;;;;;;;;0EAGf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,KAAK,SAAS;;;;;;kFACrB,8OAAC;;4EAAM,KAAK,MAAM;4EAAC;;;;;;;;;;;;;0EAErB,8OAAC;gEAAE,WAAU;0EACV,KAAK,OAAO;;;;;;0EAEf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;kFAAc;;;;;;kFAG1D,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;kFAAc;;;;;;kFAG1D,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;kFAAc;;;;;;;;;;;;;;;;;;mDAtBrD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCtC", "debugId": null}}, {"offset": {"line": 5929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/components/settings/settings-panel.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Slider } from \"@/components/ui/slider\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { \n  Settings, \n  Zap, \n  Brain, \n  Shield, \n  Palette,\n  Key,\n  Globe,\n  Save,\n  RotateCcw\n} from \"lucide-react\";\n\ninterface ModelConfig {\n  id: string;\n  name: string;\n  provider: string;\n  strengths: string[];\n  weaknesses: string[];\n  maxTokens: number;\n  costPer1k: number;\n  speed: \"Fast\" | \"Medium\" | \"Slow\";\n  quality: \"High\" | \"Medium\" | \"Low\";\n}\n\nconst availableModels: ModelConfig[] = [\n  {\n    id: \"gpt-4o\",\n    name: \"GPT-4o\",\n    provider: \"OpenAI\",\n    strengths: [\"Reasoning\", \"Code\", \"Analysis\"],\n    weaknesses: [\"Cost\", \"Speed\"],\n    maxTokens: 128000,\n    costPer1k: 0.03,\n    speed: \"Medium\",\n    quality: \"High\"\n  },\n  {\n    id: \"claude-3.5-sonnet\",\n    name: \"Claude 3.5 Sonnet\", \n    provider: \"Anthropic\",\n    strengths: [\"Writing\", \"Analysis\", \"Safety\"],\n    weaknesses: [\"Code\", \"Math\"],\n    maxTokens: 200000,\n    costPer1k: 0.015,\n    speed: \"Fast\",\n    quality: \"High\"\n  },\n  {\n    id: \"gemini-pro\",\n    name: \"Gemini Pro\",\n    provider: \"Google\",\n    strengths: [\"Multimodal\", \"Speed\", \"Cost\"],\n    weaknesses: [\"Reasoning\", \"Consistency\"],\n    maxTokens: 32000,\n    costPer1k: 0.001,\n    speed: \"Fast\",\n    quality: \"Medium\"\n  }\n];\n\nexport function SettingsPanel() {\n  const [selectedModel, setSelectedModel] = useState(\"gpt-4o\");\n  const [temperature, setTemperature] = useState([0.7]);\n  const [maxTokens, setMaxTokens] = useState([2048]);\n  const [topP, setTopP] = useState([0.9]);\n  const [streamingEnabled, setStreamingEnabled] = useState(true);\n  const [autoSave, setAutoSave] = useState(true);\n\n  const selectedModelConfig = availableModels.find(m => m.id === selectedModel);\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"p-6 border-b border-border\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl font-semibold gradient-text\">Settings & Configuration</h2>\n            <p className=\"text-sm text-muted-foreground\">Customize your Catalyst experience</p>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"outline\" size=\"sm\" className=\"gap-2\">\n              <RotateCcw className=\"w-4 h-4\" />\n              Reset\n            </Button>\n            <Button size=\"sm\" className=\"gap-2\">\n              <Save className=\"w-4 h-4\" />\n              Save Changes\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      <ScrollArea className=\"flex-1\">\n        <div className=\"p-6\">\n          <Tabs defaultValue=\"models\" className=\"space-y-6\">\n            <TabsList className=\"bg-muted/20\">\n              <TabsTrigger value=\"models\" className=\"flex items-center gap-2\">\n                <Brain className=\"w-4 h-4\" />\n                AI Models\n              </TabsTrigger>\n              <TabsTrigger value=\"parameters\" className=\"flex items-center gap-2\">\n                <Settings className=\"w-4 h-4\" />\n                Parameters\n              </TabsTrigger>\n              <TabsTrigger value=\"api\" className=\"flex items-center gap-2\">\n                <Key className=\"w-4 h-4\" />\n                API Keys\n              </TabsTrigger>\n              <TabsTrigger value=\"interface\" className=\"flex items-center gap-2\">\n                <Palette className=\"w-4 h-4\" />\n                Interface\n              </TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"models\" className=\"space-y-6\">\n              <Card className=\"catalyst-border\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Brain className=\"w-5 h-5 text-catalyst-purple\" />\n                    Model Selection\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {availableModels.map((model) => (\n                    <Card \n                      key={model.id} \n                      className={`cursor-pointer transition-all duration-200 ${\n                        selectedModel === model.id \n                          ? 'ring-2 ring-catalyst-purple catalyst-glow' \n                          : 'hover:catalyst-glow'\n                      }`}\n                      onClick={() => setSelectedModel(model.id)}\n                    >\n                      <CardContent className=\"p-4\">\n                        <div className=\"flex items-start justify-between mb-3\">\n                          <div>\n                            <h4 className=\"font-semibold\">{model.name}</h4>\n                            <p className=\"text-sm text-muted-foreground\">{model.provider}</p>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {model.speed}\n                            </Badge>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {model.quality}\n                            </Badge>\n                          </div>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 gap-4 mb-3\">\n                          <div>\n                            <p className=\"text-xs text-muted-foreground mb-1\">Strengths</p>\n                            <div className=\"flex flex-wrap gap-1\">\n                              {model.strengths.map((strength) => (\n                                <Badge key={strength} variant=\"secondary\" className=\"text-xs\">\n                                  {strength}\n                                </Badge>\n                              ))}\n                            </div>\n                          </div>\n                          <div>\n                            <p className=\"text-xs text-muted-foreground mb-1\">Considerations</p>\n                            <div className=\"flex flex-wrap gap-1\">\n                              {model.weaknesses.map((weakness) => (\n                                <Badge key={weakness} variant=\"outline\" className=\"text-xs\">\n                                  {weakness}\n                                </Badge>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n                          <span>Max Tokens: {model.maxTokens.toLocaleString()}</span>\n                          <span>Cost: ${model.costPer1k}/1K tokens</span>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"parameters\" className=\"space-y-6\">\n              <Card className=\"catalyst-border\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Settings className=\"w-5 h-5 text-catalyst-purple\" />\n                    Generation Parameters\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label htmlFor=\"temperature\">Temperature</Label>\n                      <span className=\"text-sm text-muted-foreground\">{temperature[0]}</span>\n                    </div>\n                    <Slider\n                      id=\"temperature\"\n                      min={0}\n                      max={2}\n                      step={0.1}\n                      value={temperature}\n                      onValueChange={setTemperature}\n                      className=\"w-full\"\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      Controls randomness. Lower values = more focused, higher values = more creative.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label htmlFor=\"max-tokens\">Max Tokens</Label>\n                      <span className=\"text-sm text-muted-foreground\">{maxTokens[0]}</span>\n                    </div>\n                    <Slider\n                      id=\"max-tokens\"\n                      min={1}\n                      max={selectedModelConfig?.maxTokens || 4096}\n                      step={1}\n                      value={maxTokens}\n                      onValueChange={setMaxTokens}\n                      className=\"w-full\"\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      Maximum number of tokens to generate in the response.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label htmlFor=\"top-p\">Top P</Label>\n                      <span className=\"text-sm text-muted-foreground\">{topP[0]}</span>\n                    </div>\n                    <Slider\n                      id=\"top-p\"\n                      min={0}\n                      max={1}\n                      step={0.01}\n                      value={topP}\n                      onValueChange={setTopP}\n                      className=\"w-full\"\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      Controls diversity via nucleus sampling. Lower values = more focused.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label htmlFor=\"streaming\">Enable Streaming</Label>\n                        <p className=\"text-xs text-muted-foreground\">Show responses as they generate</p>\n                      </div>\n                      <Switch\n                        id=\"streaming\"\n                        checked={streamingEnabled}\n                        onCheckedChange={setStreamingEnabled}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label htmlFor=\"auto-save\">Auto-save Sessions</Label>\n                        <p className=\"text-xs text-muted-foreground\">Automatically save conversation history</p>\n                      </div>\n                      <Switch\n                        id=\"auto-save\"\n                        checked={autoSave}\n                        onCheckedChange={setAutoSave}\n                      />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"api\" className=\"space-y-6\">\n              <Card className=\"catalyst-border\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Key className=\"w-5 h-5 text-catalyst-purple\" />\n                    API Configuration\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"openai-key\">OpenAI API Key</Label>\n                    <Input\n                      id=\"openai-key\"\n                      type=\"password\"\n                      placeholder=\"sk-...\"\n                      className=\"catalyst-border\"\n                    />\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"anthropic-key\">Anthropic API Key</Label>\n                    <Input\n                      id=\"anthropic-key\"\n                      type=\"password\"\n                      placeholder=\"sk-ant-...\"\n                      className=\"catalyst-border\"\n                    />\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"openrouter-key\">OpenRouter API Key</Label>\n                    <Input\n                      id=\"openrouter-key\"\n                      type=\"password\"\n                      placeholder=\"sk-or-...\"\n                      className=\"catalyst-border\"\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"interface\" className=\"space-y-6\">\n              <Card className=\"catalyst-border\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Palette className=\"w-5 h-5 text-catalyst-purple\" />\n                    Interface Preferences\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <Label>Dark Mode</Label>\n                      <p className=\"text-xs text-muted-foreground\">Use dark theme</p>\n                    </div>\n                    <Switch defaultChecked />\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <Label>Compact Mode</Label>\n                      <p className=\"text-xs text-muted-foreground\">Reduce spacing and padding</p>\n                    </div>\n                    <Switch />\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <Label>Show Tooltips</Label>\n                      <p className=\"text-xs text-muted-foreground\">Display helpful tooltips</p>\n                    </div>\n                    <Switch defaultChecked />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;;AAoCA,MAAM,kBAAiC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;YAAC;YAAa;YAAQ;SAAW;QAC5C,YAAY;YAAC;YAAQ;SAAQ;QAC7B,WAAW;QACX,WAAW;QACX,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;YAAC;YAAW;YAAY;SAAS;QAC5C,YAAY;YAAC;YAAQ;SAAO;QAC5B,WAAW;QACX,WAAW;QACX,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,WAAW;YAAC;YAAc;YAAS;SAAO;QAC1C,YAAY;YAAC;YAAa;SAAc;QACxC,WAAW;QACX,WAAW;QACX,OAAO;QACP,SAAS;IACX;CACD;AAEM,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAI;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAK;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAI;IACtC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE/D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;;sDAC1B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAS,WAAU;;0CACpC,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,WAAU;;0DACpC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAM,WAAU;;0DACjC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKnC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC,gIAAA,CAAA,OAAI;oDAEH,WAAW,CAAC,2CAA2C,EACrD,kBAAkB,MAAM,EAAE,GACtB,8CACA,uBACJ;oDACF,SAAS,IAAM,iBAAiB,MAAM,EAAE;8DAExC,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAiB,MAAM,IAAI;;;;;;0FACzC,8OAAC;gFAAE,WAAU;0FAAiC,MAAM,QAAQ;;;;;;;;;;;;kFAE9D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,MAAM,KAAK;;;;;;0FAEd,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,MAAM,OAAO;;;;;;;;;;;;;;;;;;0EAKpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAqC;;;;;;0FAClD,8OAAC;gFAAI,WAAU;0FACZ,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,yBACpB,8OAAC,iIAAA,CAAA,QAAK;wFAAgB,SAAQ;wFAAY,WAAU;kGACjD;uFADS;;;;;;;;;;;;;;;;kFAMlB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAqC;;;;;;0FAClD,8OAAC;gFAAI,WAAU;0FACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,yBACrB,8OAAC,iIAAA,CAAA,QAAK;wFAAgB,SAAQ;wFAAU,WAAU;kGAC/C;uFADS;;;;;;;;;;;;;;;;;;;;;;0EAQpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK;4EAAa,MAAM,SAAS,CAAC,cAAc;;;;;;;kFACjD,8OAAC;;4EAAK;4EAAQ,MAAM,SAAS;4EAAC;;;;;;;;;;;;;;;;;;;mDAjD7B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0CA0DvB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;0CACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAIzD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAc;;;;;;8EAC7B,8OAAC;oEAAK,WAAU;8EAAiC,WAAW,CAAC,EAAE;;;;;;;;;;;;sEAEjE,8OAAC,kIAAA,CAAA,SAAM;4DACL,IAAG;4DACH,KAAK;4DACL,KAAK;4DACL,MAAM;4DACN,OAAO;4DACP,eAAe;4DACf,WAAU;;;;;;sEAEZ,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAK/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAa;;;;;;8EAC5B,8OAAC;oEAAK,WAAU;8EAAiC,SAAS,CAAC,EAAE;;;;;;;;;;;;sEAE/D,8OAAC,kIAAA,CAAA,SAAM;4DACL,IAAG;4DACH,KAAK;4DACL,KAAK,qBAAqB,aAAa;4DACvC,MAAM;4DACN,OAAO;4DACP,eAAe;4DACf,WAAU;;;;;;sEAEZ,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAK/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAQ;;;;;;8EACvB,8OAAC;oEAAK,WAAU;8EAAiC,IAAI,CAAC,EAAE;;;;;;;;;;;;sEAE1D,8OAAC,kIAAA,CAAA,SAAM;4DACL,IAAG;4DACH,KAAK;4DACL,KAAK;4DACL,MAAM;4DACN,OAAO;4DACP,eAAe;4DACf,WAAU;;;;;;sEAEZ,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAK/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAY;;;;;;sFAC3B,8OAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;8EAE/C,8OAAC,kIAAA,CAAA,SAAM;oEACL,IAAG;oEACH,SAAS;oEACT,iBAAiB;;;;;;;;;;;;sEAIrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAY;;;;;;sFAC3B,8OAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;8EAE/C,8OAAC,kIAAA,CAAA,SAAM;oEACL,IAAG;oEACH,SAAS;oEACT,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAM,WAAU;0CACjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAIpD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa;;;;;;sEAC5B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAIxD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;8DAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,8OAAC,kIAAA,CAAA,SAAM;;;;;;;;;;;8DAGT,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAE/C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}, {"offset": {"line": 7155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/VSCode_Projects/Catalyst/catalyst/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from \"@/components/ui/tabs\";\nimport { <PERSON>, Zap, Settings } from \"lucide-react\";\nimport { Head<PERSON> } from \"@/components/layout/header\";\nimport { Sidebar } from \"@/components/layout/sidebar\";\nimport { ModuleWorkspace } from \"@/components/workspace/module-workspace\";\nimport { OutputPanel } from \"@/components/output/output-panel\";\nimport { SettingsPanel } from \"@/components/settings/settings-panel\";\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState(\"workspace\");\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-catalyst-dark text-foreground\">\n      <Header />\n\n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        {/* Module Library Sidebar */}\n        <Sidebar\n          collapsed={sidebarCollapsed}\n          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\n        />\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"flex-1 flex flex-col\">\n            <div className=\"border-b border-border px-4 py-2\">\n              <TabsList className=\"bg-muted/20\">\n                <TabsTrigger value=\"workspace\" className=\"flex items-center gap-2\">\n                  <Link className=\"w-4 h-4\" />\n                  Workspace\n                </TabsTrigger>\n                <TabsTrigger value=\"output\" className=\"flex items-center gap-2\">\n                  <Zap className=\"w-4 h-4\" />\n                  Output\n                </TabsTrigger>\n                <TabsTrigger value=\"settings\" className=\"flex items-center gap-2\">\n                  <Settings className=\"w-4 h-4\" />\n                  Settings\n                </TabsTrigger>\n              </TabsList>\n            </div>\n\n            <div className=\"flex-1 overflow-hidden\">\n              <TabsContent value=\"workspace\" className=\"h-full m-0\">\n                <ModuleWorkspace />\n              </TabsContent>\n\n              <TabsContent value=\"output\" className=\"h-full m-0\">\n                <OutputPanel />\n              </TabsContent>\n\n              <TabsContent value=\"settings\" className=\"h-full m-0\">\n                <SettingsPanel />\n              </TabsContent>\n            </div>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,uIAAA,CAAA,UAAO;wBACN,WAAW;wBACX,kBAAkB,IAAM,oBAAoB,CAAC;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAY,WAAU;;kEACvC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAS,WAAU;;kEACpC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG7B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;;kEACtC,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;8CAMtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAY,WAAU;sDACvC,cAAA,8OAAC,sJAAA,CAAA,kBAAe;;;;;;;;;;sDAGlB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,WAAU;sDACpC,cAAA,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;sDAGd,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;sDACtC,cAAA,8OAAC,mJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9B", "debugId": null}}]}