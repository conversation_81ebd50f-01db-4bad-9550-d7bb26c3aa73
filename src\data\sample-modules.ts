// Sample .promptx modules for Catalyst

import { PromptxModule } from '@/types/promptx';

export const SAMPLE_MODULES: PromptxModule[] = [
  {
    schema: "1.0.0",
    metadata: {
      name: "Content Writer",
      description: "Professional content writing assistant for articles, blogs, and marketing copy",
      version: "1.2.0",
      author: "Catalyst Team",
      tags: ["writing", "content", "marketing", "blog"],
      category: "core",
      modelAffinity: ["gpt-4o", "claude-3.5-sonnet"],
      formattingStyle: "markdown",
      type: "single",
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-20T14:30:00Z"
    },
    content: {
      systemPrompt: "You are an expert content writer with years of experience in creating engaging, well-structured content across various industries. You understand SEO principles, audience engagement, and brand voice consistency.",
      userPrompt: "Write a {{content_type}} about {{topic}} for {{audience}}.\n\nTone: {{tone}}\nLength: {{length}}\nFormat: {{format}}\n\nAdditional requirements:\n{{requirements}}",
      variables: [
        {
          name: "content_type",
          type: "select",
          description: "Type of content to create",
          required: true,
          options: ["blog post", "article", "social media post", "email newsletter", "product description", "landing page copy"],
          defaultValue: "blog post"
        },
        {
          name: "topic",
          type: "text",
          description: "Main topic or subject",
          required: true,
          placeholder: "e.g., sustainable technology trends"
        },
        {
          name: "audience",
          type: "text",
          description: "Target audience",
          required: true,
          placeholder: "e.g., tech professionals, small business owners"
        },
        {
          name: "tone",
          type: "select",
          description: "Writing tone and style",
          required: false,
          options: ["professional", "casual", "friendly", "authoritative", "conversational", "technical"],
          defaultValue: "professional"
        },
        {
          name: "length",
          type: "select",
          description: "Content length",
          required: false,
          options: ["short (300-500 words)", "medium (500-1000 words)", "long (1000-2000 words)", "comprehensive (2000+ words)"],
          defaultValue: "medium (500-1000 words)"
        },
        {
          name: "format",
          type: "select",
          description: "Output format preference",
          required: false,
          options: ["markdown with headers", "plain text", "HTML structure", "bullet points", "numbered sections"],
          defaultValue: "markdown with headers"
        },
        {
          name: "requirements",
          type: "text",
          description: "Any specific requirements or guidelines",
          required: false,
          placeholder: "e.g., include statistics, mention specific products, SEO keywords"
        }
      ],
      contextRetention: true
    }
  },
  {
    schema: "1.0.0",
    metadata: {
      name: "Code Analyzer",
      description: "Analyzes code for bugs, performance issues, and best practices",
      version: "1.1.0",
      author: "Catalyst Team",
      tags: ["coding", "debugging", "analysis", "review"],
      category: "specialized",
      modelAffinity: ["gpt-4o", "claude-3.5-sonnet"],
      formattingStyle: "markdown",
      type: "single",
      createdAt: "2024-01-10T09:00:00Z",
      updatedAt: "2024-01-18T16:45:00Z"
    },
    content: {
      systemPrompt: "You are a senior software engineer and code reviewer with expertise across multiple programming languages. You excel at identifying bugs, security vulnerabilities, performance issues, and suggesting improvements following best practices.",
      userPrompt: "Analyze the following {{language}} code for:\n- Bugs and potential issues\n- Performance optimizations\n- Security vulnerabilities\n- Code quality and best practices\n- {{focus_area}}\n\n```{{language}}\n{{code}}\n```\n\nProvide specific recommendations with examples where possible.",
      variables: [
        {
          name: "language",
          type: "select",
          description: "Programming language",
          required: true,
          options: ["JavaScript", "TypeScript", "Python", "Java", "C#", "Go", "Rust", "PHP", "Ruby", "C++"],
          defaultValue: "JavaScript"
        },
        {
          name: "code",
          type: "text",
          description: "Code to analyze",
          required: true,
          placeholder: "Paste your code here..."
        },
        {
          name: "focus_area",
          type: "select",
          description: "Specific area to focus on",
          required: false,
          options: ["General review", "Performance optimization", "Security audit", "Refactoring suggestions", "Testing recommendations", "Documentation review"],
          defaultValue: "General review"
        }
      ],
      contextRetention: false
    }
  },
  {
    schema: "1.0.0",
    metadata: {
      name: "Meeting Summarizer",
      description: "Creates structured summaries of meetings with action items and key decisions",
      version: "1.0.0",
      author: "Catalyst Team",
      tags: ["meetings", "summary", "productivity", "business"],
      category: "specialized",
      modelAffinity: ["gpt-4o", "claude-3.5-sonnet"],
      formattingStyle: "markdown",
      type: "single",
      createdAt: "2024-01-12T11:30:00Z",
      updatedAt: "2024-01-12T11:30:00Z"
    },
    content: {
      systemPrompt: "You are an executive assistant skilled at creating clear, actionable meeting summaries. You excel at identifying key decisions, action items, and important discussion points from meeting transcripts or notes.",
      userPrompt: "Create a structured summary of this {{meeting_type}} meeting:\n\n{{meeting_content}}\n\nInclude:\n- Meeting overview\n- Key decisions made\n- Action items with owners and deadlines\n- Important discussion points\n- Next steps\n\nFormat: {{output_format}}",
      variables: [
        {
          name: "meeting_type",
          type: "select",
          description: "Type of meeting",
          required: true,
          options: ["team standup", "project review", "client meeting", "board meeting", "brainstorming session", "one-on-one", "all-hands"],
          defaultValue: "team standup"
        },
        {
          name: "meeting_content",
          type: "text",
          description: "Meeting transcript, notes, or recording",
          required: true,
          placeholder: "Paste meeting transcript or detailed notes here..."
        },
        {
          name: "output_format",
          type: "select",
          description: "Summary format",
          required: false,
          options: ["structured markdown", "bullet points", "executive summary", "detailed report"],
          defaultValue: "structured markdown"
        }
      ],
      contextRetention: false
    }
  },
  {
    schema: "1.0.0",
    metadata: {
      name: "Research Assistant",
      description: "Comprehensive research and analysis on any topic with citations and insights",
      version: "1.3.0",
      author: "Catalyst Team",
      tags: ["research", "analysis", "academic", "insights"],
      category: "core",
      modelAffinity: ["gpt-4o", "claude-3.5-sonnet"],
      formattingStyle: "markdown",
      type: "single",
      createdAt: "2024-01-08T14:20:00Z",
      updatedAt: "2024-01-22T09:15:00Z"
    },
    content: {
      systemPrompt: "You are a research specialist with expertise in gathering, analyzing, and synthesizing information from multiple sources. You provide well-structured research with proper citations and actionable insights.",
      userPrompt: "Research {{topic}} with focus on {{research_focus}}.\n\nScope: {{scope}}\nDepth: {{depth}}\nAudience: {{audience}}\n\nProvide:\n1. Executive summary\n2. Key findings\n3. Detailed analysis\n4. Recommendations\n5. Sources and further reading\n\nSpecial requirements: {{requirements}}",
      variables: [
        {
          name: "topic",
          type: "text",
          description: "Research topic",
          required: true,
          placeholder: "e.g., impact of AI on healthcare industry"
        },
        {
          name: "research_focus",
          type: "select",
          description: "Primary research focus",
          required: true,
          options: ["market trends", "competitive analysis", "technology assessment", "policy implications", "financial impact", "social implications", "future predictions"],
          defaultValue: "market trends"
        },
        {
          name: "scope",
          type: "select",
          description: "Research scope",
          required: false,
          options: ["global", "regional", "national", "industry-specific", "company-specific"],
          defaultValue: "industry-specific"
        },
        {
          name: "depth",
          type: "select",
          description: "Research depth",
          required: false,
          options: ["overview", "detailed", "comprehensive", "expert-level"],
          defaultValue: "detailed"
        },
        {
          name: "audience",
          type: "text",
          description: "Target audience",
          required: false,
          placeholder: "e.g., executives, investors, technical team",
          defaultValue: "business professionals"
        },
        {
          name: "requirements",
          type: "text",
          description: "Special requirements or constraints",
          required: false,
          placeholder: "e.g., focus on recent developments, include financial data"
        }
      ],
      contextRetention: true
    }
  },
  {
    schema: "1.0.0",
    metadata: {
      name: "Email Composer",
      description: "Professional email writing for various business contexts",
      version: "1.1.0",
      author: "Catalyst Team",
      tags: ["email", "communication", "business", "professional"],
      category: "core",
      modelAffinity: ["gpt-4o", "claude-3.5-sonnet"],
      formattingStyle: "plaintext",
      type: "single",
      createdAt: "2024-01-14T08:45:00Z",
      updatedAt: "2024-01-19T13:20:00Z"
    },
    content: {
      systemPrompt: "You are a professional communication expert who writes clear, effective emails that achieve their intended purpose while maintaining appropriate tone and professionalism.",
      userPrompt: "Write a {{email_type}} email:\n\nTo: {{recipient}}\nSubject: {{subject}}\nPurpose: {{purpose}}\nTone: {{tone}}\nKey points to include:\n{{key_points}}\n\nContext: {{context}}",
      variables: [
        {
          name: "email_type",
          type: "select",
          description: "Type of email",
          required: true,
          options: ["business inquiry", "follow-up", "meeting request", "proposal", "complaint", "thank you", "introduction", "update", "request for information"],
          defaultValue: "business inquiry"
        },
        {
          name: "recipient",
          type: "text",
          description: "Email recipient",
          required: true,
          placeholder: "e.g., John Smith, CEO of TechCorp"
        },
        {
          name: "subject",
          type: "text",
          description: "Email subject (optional - will be suggested if empty)",
          required: false,
          placeholder: "Leave empty for AI-generated subject"
        },
        {
          name: "purpose",
          type: "text",
          description: "Main purpose of the email",
          required: true,
          placeholder: "e.g., schedule a demo meeting, request partnership information"
        },
        {
          name: "tone",
          type: "select",
          description: "Email tone",
          required: false,
          options: ["formal", "professional", "friendly", "casual", "urgent", "diplomatic"],
          defaultValue: "professional"
        },
        {
          name: "key_points",
          type: "text",
          description: "Key points to include",
          required: true,
          placeholder: "List the main points you want to communicate"
        },
        {
          name: "context",
          type: "text",
          description: "Additional context or background",
          required: false,
          placeholder: "Any relevant background information"
        }
      ],
      contextRetention: false
    }
  }
];

// Helper function to get modules by category
export function getModulesByCategory(category: string): PromptxModule[] {
  return SAMPLE_MODULES.filter(module => module.metadata.category === category);
}

// Helper function to get modules by tag
export function getModulesByTag(tag: string): PromptxModule[] {
  return SAMPLE_MODULES.filter(module => module.metadata.tags.includes(tag));
}

// Helper function to search modules
export function searchModules(query: string): PromptxModule[] {
  const lowercaseQuery = query.toLowerCase();
  return SAMPLE_MODULES.filter(module => 
    module.metadata.name.toLowerCase().includes(lowercaseQuery) ||
    module.metadata.description.toLowerCase().includes(lowercaseQuery) ||
    module.metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}
