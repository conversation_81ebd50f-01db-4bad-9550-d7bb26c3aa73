"use client"

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Eye, 
  Code,
  Settings,
  Play,
  Download
} from 'lucide-react';
import { PromptxModule, PromptxVariable, COMMON_VARIABLES } from '@/types/promptx';
import { PromptxUtils } from '@/lib/promptx';

interface ModuleEditorProps {
  module: PromptxModule | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (module: PromptxModule) => void;
  onExecute?: (module: PromptxModule) => void;
}

export function ModuleEditor({ module, isOpen, onClose, onSave, onExecute }: ModuleEditorProps) {
  const [editedModule, setEditedModule] = useState<PromptxModule | null>(null);
  const [previewValues, setPreviewValues] = useState<Record<string, any>>({});
  const [activeTab, setActiveTab] = useState('content');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (module) {
      setEditedModule({ ...module });
      // Initialize preview values with defaults
      const defaultValues: Record<string, any> = {};
      module.content.variables.forEach(variable => {
        if (variable.defaultValue !== undefined) {
          defaultValues[variable.name] = variable.defaultValue;
        }
      });
      setPreviewValues(defaultValues);
    }
  }, [module]);

  if (!isOpen || !editedModule) return null;

  const handleSave = () => {
    const validation = PromptxUtils.validate(editedModule);
    if (validation.valid) {
      onSave(editedModule);
      onClose();
    } else {
      setValidationErrors(validation.errors.map(e => `${e.path}: ${e.message}`));
    }
  };

  const handleMetadataChange = (field: string, value: any) => {
    setEditedModule(prev => prev ? {
      ...prev,
      metadata: {
        ...prev.metadata,
        [field]: value,
        updatedAt: new Date().toISOString()
      }
    } : null);
  };

  const handleContentChange = (field: string, value: any) => {
    setEditedModule(prev => prev ? {
      ...prev,
      content: {
        ...prev.content,
        [field]: value
      }
    } : null);
  };

  const addVariable = (template?: PromptxVariable) => {
    const newVariable: PromptxVariable = template || {
      name: 'newVariable',
      type: 'text',
      description: 'New variable',
      required: false
    };

    setEditedModule(prev => prev ? {
      ...prev,
      content: {
        ...prev.content,
        variables: [...prev.content.variables, newVariable]
      }
    } : null);
  };

  const updateVariable = (index: number, field: string, value: any) => {
    setEditedModule(prev => prev ? {
      ...prev,
      content: {
        ...prev.content,
        variables: prev.content.variables.map((variable, i) => 
          i === index ? { ...variable, [field]: value } : variable
        )
      }
    } : null);
  };

  const removeVariable = (index: number) => {
    setEditedModule(prev => prev ? {
      ...prev,
      content: {
        ...prev.content,
        variables: prev.content.variables.filter((_, i) => i !== index)
      }
    } : null);
  };

  const generatePreview = () => {
    if (!editedModule) return { userPrompt: '', systemPrompt: '' };
    return PromptxUtils.generate(editedModule, previewValues);
  };

  const extractedVariables = PromptxUtils.extract(editedModule.content.userPrompt + (editedModule.content.systemPrompt || ''));

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden catalyst-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div>
            <CardTitle className="text-xl">Edit Module</CardTitle>
            <CardDescription>
              {editedModule.metadata.name} • {editedModule.metadata.type} module
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {onExecute && (
              <Button size="sm" onClick={() => onExecute(editedModule)}>
                <Play className="w-4 h-4 mr-2" />
                Test
              </Button>
            )}
            <Button size="sm" variant="outline" onClick={() => {
              const content = PromptxUtils.serialize(editedModule);
              const blob = new Blob([content], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `${editedModule.metadata.name.replace(/[^a-zA-Z0-9]/g, '_')}.promptx`;
              a.click();
              URL.revokeObjectURL(url);
            }}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button size="sm" variant="ghost" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="overflow-auto max-h-[calc(90vh-120px)]">
          {validationErrors.length > 0 && (
            <Card className="border-red-500/50 bg-red-500/10 mb-4">
              <CardContent className="pt-4">
                <div className="text-sm text-red-300 space-y-1">
                  {validationErrors.map((error, index) => (
                    <div key={index}>{error}</div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="content">
                <Code className="w-4 h-4 mr-2" />
                Content
              </TabsTrigger>
              <TabsTrigger value="variables">
                <Settings className="w-4 h-4 mr-2" />
                Variables
              </TabsTrigger>
              <TabsTrigger value="metadata">
                <Settings className="w-4 h-4 mr-2" />
                Metadata
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="systemPrompt">System Prompt (Optional)</Label>
                  <textarea
                    id="systemPrompt"
                    value={editedModule.content.systemPrompt || ''}
                    onChange={(e) => handleContentChange('systemPrompt', e.target.value)}
                    className="w-full h-32 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm"
                    placeholder="Enter system prompt..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="userPrompt">User Prompt *</Label>
                  <textarea
                    id="userPrompt"
                    value={editedModule.content.userPrompt}
                    onChange={(e) => handleContentChange('userPrompt', e.target.value)}
                    className="w-full h-40 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm"
                    placeholder="Enter user prompt with {{variables}}..."
                  />
                  <div className="mt-2 text-xs text-muted-foreground">
                    Detected variables: {extractedVariables.length > 0 ? extractedVariables.join(', ') : 'None'}
                  </div>
                </div>

                <div>
                  <Label htmlFor="assistantPrompt">Assistant Prompt (Optional)</Label>
                  <textarea
                    id="assistantPrompt"
                    value={editedModule.content.assistantPrompt || ''}
                    onChange={(e) => handleContentChange('assistantPrompt', e.target.value)}
                    className="w-full h-24 mt-2 p-3 bg-card border border-border rounded-md resize-none font-mono text-sm"
                    placeholder="Enter assistant prompt for few-shot examples..."
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="variables" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Variables</h3>
                <div className="flex gap-2">
                  <select
                    onChange={(e) => {
                      if (e.target.value && COMMON_VARIABLES[e.target.value]) {
                        addVariable(COMMON_VARIABLES[e.target.value]);
                        e.target.value = '';
                      }
                    }}
                    className="px-3 py-1 bg-card border border-border rounded text-sm"
                  >
                    <option value="">Add Common Variable</option>
                    {Object.keys(COMMON_VARIABLES).map(key => (
                      <option key={key} value={key}>{key}</option>
                    ))}
                  </select>
                  <Button size="sm" onClick={() => addVariable()}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Variable
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                {editedModule.content.variables.map((variable, index) => (
                  <Card key={index} className="p-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Name</Label>
                        <Input
                          value={variable.name}
                          onChange={(e) => updateVariable(index, 'name', e.target.value)}
                          placeholder="variableName"
                        />
                      </div>
                      <div>
                        <Label>Type</Label>
                        <select
                          value={variable.type}
                          onChange={(e) => updateVariable(index, 'type', e.target.value)}
                          className="w-full px-3 py-2 bg-card border border-border rounded"
                        >
                          <option value="text">Text</option>
                          <option value="number">Number</option>
                          <option value="boolean">Boolean</option>
                          <option value="select">Select</option>
                          <option value="multiselect">Multi-select</option>
                        </select>
                      </div>
                      <div className="col-span-2">
                        <Label>Description</Label>
                        <Input
                          value={variable.description || ''}
                          onChange={(e) => updateVariable(index, 'description', e.target.value)}
                          placeholder="Variable description"
                        />
                      </div>
                      {(variable.type === 'select' || variable.type === 'multiselect') && (
                        <div className="col-span-2">
                          <Label>Options (comma-separated)</Label>
                          <Input
                            value={variable.options?.join(', ') || ''}
                            onChange={(e) => updateVariable(index, 'options', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                            placeholder="option1, option2, option3"
                          />
                        </div>
                      )}
                      <div className="col-span-2 flex justify-between items-center">
                        <div className="flex items-center gap-4">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={variable.required || false}
                              onChange={(e) => updateVariable(index, 'required', e.target.checked)}
                            />
                            Required
                          </label>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeVariable(index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="metadata" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={editedModule.metadata.name}
                    onChange={(e) => handleMetadataChange('name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={editedModule.metadata.version}
                    onChange={(e) => handleMetadataChange('version', e.target.value)}
                    placeholder="1.0.0"
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="description">Description *</Label>
                  <textarea
                    id="description"
                    value={editedModule.metadata.description}
                    onChange={(e) => handleMetadataChange('description', e.target.value)}
                    className="w-full h-20 mt-2 p-3 bg-card border border-border rounded-md resize-none"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={editedModule.metadata.category}
                    onChange={(e) => handleMetadataChange('category', e.target.value)}
                    className="w-full px-3 py-2 bg-card border border-border rounded"
                  >
                    <option value="core">Core</option>
                    <option value="engine">Engine</option>
                    <option value="specialized">Specialized</option>
                    <option value="custom">Custom</option>
                    <option value="template">Template</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="formattingStyle">Formatting Style</Label>
                  <select
                    id="formattingStyle"
                    value={editedModule.metadata.formattingStyle}
                    onChange={(e) => handleMetadataChange('formattingStyle', e.target.value)}
                    className="w-full px-3 py-2 bg-card border border-border rounded"
                  >
                    <option value="markdown">Markdown</option>
                    <option value="plaintext">Plain Text</option>
                    <option value="json">JSON</option>
                    <option value="html">HTML</option>
                  </select>
                </div>
                <div className="col-span-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    value={editedModule.metadata.tags.join(', ')}
                    onChange={(e) => handleMetadataChange('tags', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Variable Values</h3>
                  <div className="space-y-3">
                    {editedModule.content.variables.map((variable) => (
                      <div key={variable.name}>
                        <Label>{variable.name} {variable.required && '*'}</Label>
                        {variable.type === 'select' ? (
                          <select
                            value={previewValues[variable.name] || ''}
                            onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.value }))}
                            className="w-full px-3 py-2 bg-card border border-border rounded mt-1"
                          >
                            <option value="">Select...</option>
                            {variable.options?.map(option => (
                              <option key={option} value={option}>{option}</option>
                            ))}
                          </select>
                        ) : variable.type === 'boolean' ? (
                          <div className="mt-1">
                            <label className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={previewValues[variable.name] || false}
                                onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.checked }))}
                              />
                              {variable.description || variable.name}
                            </label>
                          </div>
                        ) : (
                          <Input
                            type={variable.type === 'number' ? 'number' : 'text'}
                            value={previewValues[variable.name] || ''}
                            onChange={(e) => setPreviewValues(prev => ({ ...prev, [variable.name]: e.target.value }))}
                            placeholder={variable.placeholder || variable.description}
                            className="mt-1"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Generated Prompt</h3>
                  <div className="space-y-4">
                    {generatePreview().systemPrompt && (
                      <div>
                        <Label>System Prompt</Label>
                        <div className="mt-1 p-3 bg-muted rounded border font-mono text-sm whitespace-pre-wrap">
                          {generatePreview().systemPrompt}
                        </div>
                      </div>
                    )}
                    <div>
                      <Label>User Prompt</Label>
                      <div className="mt-1 p-3 bg-muted rounded border font-mono text-sm whitespace-pre-wrap">
                        {generatePreview().userPrompt}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
