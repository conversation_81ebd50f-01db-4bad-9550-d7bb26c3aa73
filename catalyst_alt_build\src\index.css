
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Catalyst Design System */

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 263 70% 65%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 263 70% 65%;

    --radius: 0.75rem;

    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 263 70% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 263 70% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-catalyst-dark text-foreground font-sans;
    background-attachment: fixed;
  }

  .catalyst-glow {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }

  .catalyst-border {
    border: 1px solid rgba(168, 85, 247, 0.3);
  }
}

@layer components {
  .module-card {
    @apply bg-card/50 backdrop-blur-sm border catalyst-border rounded-lg p-4 transition-all duration-200 hover:catalyst-glow hover:scale-[1.02];
  }

  .prompt-panel {
    @apply bg-gradient-to-br from-catalyst-dark to-catalyst-dark-secondary border catalyst-border rounded-lg;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-catalyst-blue to-catalyst-purple bg-clip-text text-transparent;
  }
}
