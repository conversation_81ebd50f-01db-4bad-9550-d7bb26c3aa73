// .promptx Module System Types and Schema

export interface PromptxVariable {
  name: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect';
  description?: string;
  required?: boolean;
  defaultValue?: string | number | boolean;
  options?: string[]; // For select/multiselect types
  placeholder?: string;
}

export interface PromptxMetadata {
  name: string;
  description: string;
  version: string;
  author?: string;
  tags: string[];
  category: string;
  modelAffinity?: string[]; // Preferred models (e.g., "gpt-4o", "claude-3.5-sonnet")
  formattingStyle: 'markdown' | 'plaintext' | 'json' | 'html';
  type: 'single' | 'chained' | 'template';
  uiViewPreferences?: {
    compact?: boolean;
    fullView?: boolean;
    toggled?: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PromptxContent {
  systemPrompt?: string;
  userPrompt: string;
  assistantPrompt?: string; // For few-shot examples
  variables: PromptxVariable[];
  chainOrder?: number; // For chained modules
  contextRetention?: boolean; // Whether to retain context for next module
}

export interface PromptxModule {
  metadata: PromptxMetadata;
  content: PromptxContent;
  schema: string; // Schema version for validation
}

// JSON Schema for .promptx validation
export const PROMPTX_SCHEMA = {
  $schema: "http://json-schema.org/draft-07/schema#",
  title: "Catalyst .promptx Module",
  type: "object",
  required: ["metadata", "content", "schema"],
  properties: {
    schema: {
      type: "string",
      const: "1.0.0"
    },
    metadata: {
      type: "object",
      required: ["name", "description", "version", "tags", "category", "formattingStyle", "type", "createdAt", "updatedAt"],
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 100
        },
        description: {
          type: "string",
          minLength: 1,
          maxLength: 500
        },
        version: {
          type: "string",
          pattern: "^\\d+\\.\\d+\\.\\d+$"
        },
        author: {
          type: "string",
          maxLength: 100
        },
        tags: {
          type: "array",
          items: {
            type: "string",
            minLength: 1,
            maxLength: 50
          },
          maxItems: 20
        },
        category: {
          type: "string",
          enum: ["core", "engine", "specialized", "custom", "template"]
        },
        modelAffinity: {
          type: "array",
          items: {
            type: "string",
            enum: [
              "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo",
              "claude-3.5-sonnet", "claude-3-opus", "claude-3-haiku",
              "gemini-pro", "gemini-flash", "llama-3.1-70b", "llama-3.1-8b"
            ]
          }
        },
        formattingStyle: {
          type: "string",
          enum: ["markdown", "plaintext", "json", "html"]
        },
        type: {
          type: "string",
          enum: ["single", "chained", "template"]
        },
        uiViewPreferences: {
          type: "object",
          properties: {
            compact: { type: "boolean" },
            fullView: { type: "boolean" },
            toggled: { type: "boolean" }
          },
          additionalProperties: false
        },
        createdAt: {
          type: "string",
          format: "date-time"
        },
        updatedAt: {
          type: "string",
          format: "date-time"
        }
      },
      additionalProperties: false
    },
    content: {
      type: "object",
      required: ["userPrompt", "variables"],
      properties: {
        systemPrompt: {
          type: "string",
          maxLength: 10000
        },
        userPrompt: {
          type: "string",
          minLength: 1,
          maxLength: 10000
        },
        assistantPrompt: {
          type: "string",
          maxLength: 10000
        },
        variables: {
          type: "array",
          items: {
            type: "object",
            required: ["name", "type"],
            properties: {
              name: {
                type: "string",
                pattern: "^[a-zA-Z][a-zA-Z0-9_]*$",
                minLength: 1,
                maxLength: 50
              },
              type: {
                type: "string",
                enum: ["text", "number", "boolean", "select", "multiselect"]
              },
              description: {
                type: "string",
                maxLength: 200
              },
              required: {
                type: "boolean",
                default: false
              },
              defaultValue: {
                oneOf: [
                  { type: "string" },
                  { type: "number" },
                  { type: "boolean" }
                ]
              },
              options: {
                type: "array",
                items: {
                  type: "string",
                  minLength: 1,
                  maxLength: 100
                },
                maxItems: 50
              },
              placeholder: {
                type: "string",
                maxLength: 100
              }
            },
            additionalProperties: false
          }
        },
        chainOrder: {
          type: "number",
          minimum: 0,
          maximum: 100
        },
        contextRetention: {
          type: "boolean",
          default: true
        }
      },
      additionalProperties: false
    }
  },
  additionalProperties: false
} as const;

// Utility types for working with .promptx modules
export type PromptxValidationError = {
  path: string;
  message: string;
  value?: any;
};

export type PromptxValidationResult = {
  valid: boolean;
  errors: PromptxValidationError[];
};

// Template for creating new .promptx modules
export const createEmptyPromptxModule = (overrides?: Partial<PromptxModule>): PromptxModule => {
  const now = new Date().toISOString();
  
  return {
    schema: "1.0.0",
    metadata: {
      name: "New Module",
      description: "A new prompt module",
      version: "1.0.0",
      tags: [],
      category: "custom",
      formattingStyle: "markdown",
      type: "single",
      createdAt: now,
      updatedAt: now,
      ...overrides?.metadata
    },
    content: {
      userPrompt: "Enter your prompt here...",
      variables: [],
      contextRetention: true,
      ...overrides?.content
    },
    ...overrides
  };
};

// Common variable templates
export const COMMON_VARIABLES: Record<string, PromptxVariable> = {
  topic: {
    name: "topic",
    type: "text",
    description: "The main topic or subject",
    required: true,
    placeholder: "Enter the topic..."
  },
  tone: {
    name: "tone",
    type: "select",
    description: "The tone of voice for the output",
    required: false,
    defaultValue: "professional",
    options: ["professional", "casual", "friendly", "formal", "creative", "technical"]
  },
  audience: {
    name: "audience",
    type: "text",
    description: "Target audience for the content",
    required: false,
    placeholder: "e.g., developers, students, general public"
  },
  length: {
    name: "length",
    type: "select",
    description: "Desired length of the output",
    required: false,
    defaultValue: "medium",
    options: ["short", "medium", "long", "detailed"]
  },
  format: {
    name: "format",
    type: "select",
    description: "Output format preference",
    required: false,
    defaultValue: "markdown",
    options: ["markdown", "plaintext", "html", "json", "bullet-points", "numbered-list"]
  }
};
