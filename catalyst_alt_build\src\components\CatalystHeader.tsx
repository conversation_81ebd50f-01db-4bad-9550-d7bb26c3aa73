
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowRight, Zap, GraduationCap, Download } from "lucide-react";

const CatalystHeader = () => {
  return (
    <header className="h-16 border-b border-border bg-card/30 backdrop-blur-sm flex items-center justify-between px-6">
      {/* Logo and Brand */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <img 
            src="/lovable-uploads/893dd9f4-ffca-4755-8b83-f25dfaad05b8.png" 
            alt="Catalyst Logo" 
            className="w-8 h-8"
          />
          <div>
            <h1 className="text-xl font-bold gradient-text">Catalyst</h1>
            <p className="text-xs text-muted-foreground">Prompt Engineering Ecosystem</p>
          </div>
        </div>
        <Badge variant="secondary" className="text-xs">
          MVP v1.3.1
        </Badge>
      </div>

      {/* Quick Actions */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          Connected to OpenRouter
        </div>
        
        <Button variant="outline" size="sm" className="gap-2">
          <Download className="w-4 h-4" />
          Export Session
        </Button>

        <Button size="sm" className="gap-2 bg-catalyst-gradient hover:opacity-90">
          <GraduationCap className="w-4 h-4" />
          Prompt4Me
          <ArrowRight className="w-4 h-4" />
        </Button>

        <Avatar className="w-8 h-8">
          <AvatarFallback className="bg-catalyst-purple text-white text-xs">
            U
          </AvatarFallback>
        </Avatar>
      </div>
    </header>
  );
};

export default CatalystHeader;
