// .promptx Module System Core Functions

import { 
  PromptxModule, 
  PromptxValidationResult, 
  PromptxValidationError,
  PROMPTX_SCHEMA,
  createEmptyPromptxModule,
  PromptxVariable
} from '@/types/promptx';

// JSON Schema validation using Ajv-like validation
export function validatePromptxModule(module: any): PromptxValidationResult {
  const errors: PromptxValidationError[] = [];

  try {
    // Basic structure validation
    if (!module || typeof module !== 'object') {
      errors.push({
        path: 'root',
        message: 'Module must be a valid object',
        value: module
      });
      return { valid: false, errors };
    }

    // Schema version check
    if (module.schema !== '1.0.0') {
      errors.push({
        path: 'schema',
        message: 'Schema version must be "1.0.0"',
        value: module.schema
      });
    }

    // Metadata validation
    if (!module.metadata) {
      errors.push({
        path: 'metadata',
        message: 'Metadata is required',
        value: module.metadata
      });
    } else {
      validateMetadata(module.metadata, errors);
    }

    // Content validation
    if (!module.content) {
      errors.push({
        path: 'content',
        message: 'Content is required',
        value: module.content
      });
    } else {
      validateContent(module.content, errors);
    }

  } catch (error) {
    errors.push({
      path: 'validation',
      message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      value: error
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

function validateMetadata(metadata: any, errors: PromptxValidationError[]) {
  const required = ['name', 'description', 'version', 'tags', 'category', 'formattingStyle', 'type', 'createdAt', 'updatedAt'];
  
  for (const field of required) {
    if (!metadata[field]) {
      errors.push({
        path: `metadata.${field}`,
        message: `${field} is required`,
        value: metadata[field]
      });
    }
  }

  // Validate specific fields
  if (metadata.name && (typeof metadata.name !== 'string' || metadata.name.length === 0 || metadata.name.length > 100)) {
    errors.push({
      path: 'metadata.name',
      message: 'Name must be a string between 1 and 100 characters',
      value: metadata.name
    });
  }

  if (metadata.version && !/^\d+\.\d+\.\d+$/.test(metadata.version)) {
    errors.push({
      path: 'metadata.version',
      message: 'Version must follow semantic versioning (e.g., "1.0.0")',
      value: metadata.version
    });
  }

  if (metadata.category && !['core', 'engine', 'specialized', 'custom', 'template'].includes(metadata.category)) {
    errors.push({
      path: 'metadata.category',
      message: 'Category must be one of: core, engine, specialized, custom, template',
      value: metadata.category
    });
  }

  if (metadata.formattingStyle && !['markdown', 'plaintext', 'json', 'html'].includes(metadata.formattingStyle)) {
    errors.push({
      path: 'metadata.formattingStyle',
      message: 'Formatting style must be one of: markdown, plaintext, json, html',
      value: metadata.formattingStyle
    });
  }

  if (metadata.type && !['single', 'chained', 'template'].includes(metadata.type)) {
    errors.push({
      path: 'metadata.type',
      message: 'Type must be one of: single, chained, template',
      value: metadata.type
    });
  }
}

function validateContent(content: any, errors: PromptxValidationError[]) {
  if (!content.userPrompt || typeof content.userPrompt !== 'string' || content.userPrompt.length === 0) {
    errors.push({
      path: 'content.userPrompt',
      message: 'User prompt is required and must be a non-empty string',
      value: content.userPrompt
    });
  }

  if (!Array.isArray(content.variables)) {
    errors.push({
      path: 'content.variables',
      message: 'Variables must be an array',
      value: content.variables
    });
  } else {
    content.variables.forEach((variable: any, index: number) => {
      validateVariable(variable, `content.variables[${index}]`, errors);
    });
  }
}

function validateVariable(variable: any, path: string, errors: PromptxValidationError[]) {
  if (!variable.name || typeof variable.name !== 'string') {
    errors.push({
      path: `${path}.name`,
      message: 'Variable name is required and must be a string',
      value: variable.name
    });
  } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(variable.name)) {
    errors.push({
      path: `${path}.name`,
      message: 'Variable name must start with a letter and contain only letters, numbers, and underscores',
      value: variable.name
    });
  }

  if (!variable.type || !['text', 'number', 'boolean', 'select', 'multiselect'].includes(variable.type)) {
    errors.push({
      path: `${path}.type`,
      message: 'Variable type must be one of: text, number, boolean, select, multiselect',
      value: variable.type
    });
  }
}

// Parse .promptx file content
export function parsePromptxFile(content: string): { module: PromptxModule | null; errors: PromptxValidationError[] } {
  try {
    const parsed = JSON.parse(content);
    const validation = validatePromptxModule(parsed);
    
    if (validation.valid) {
      return { module: parsed as PromptxModule, errors: [] };
    } else {
      return { module: null, errors: validation.errors };
    }
  } catch (error) {
    return {
      module: null,
      errors: [{
        path: 'parse',
        message: `Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
        value: content
      }]
    };
  }
}

// Serialize .promptx module to file content
export function serializePromptxModule(module: PromptxModule): string {
  // Update the updatedAt timestamp
  const updatedModule = {
    ...module,
    metadata: {
      ...module.metadata,
      updatedAt: new Date().toISOString()
    }
  };

  return JSON.stringify(updatedModule, null, 2);
}

// Variable substitution in prompts
export function substituteVariables(
  prompt: string, 
  variables: Record<string, any>
): string {
  let result = prompt;
  
  // Replace {{variableName}} with actual values
  for (const [name, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\{\\{\\s*${name}\\s*\\}\\}`, 'g');
    result = result.replace(regex, String(value || ''));
  }
  
  return result;
}

// Extract variable names from prompt text
export function extractVariableNames(prompt: string): string[] {
  const regex = /\{\{\s*([a-zA-Z][a-zA-Z0-9_]*)\s*\}\}/g;
  const matches = [];
  let match;
  
  while ((match = regex.exec(prompt)) !== null) {
    if (!matches.includes(match[1])) {
      matches.push(match[1]);
    }
  }
  
  return matches;
}

// Generate a complete prompt from module and variable values
export function generatePrompt(
  module: PromptxModule,
  variableValues: Record<string, any>
): {
  systemPrompt?: string;
  userPrompt: string;
  assistantPrompt?: string;
} {
  const { content } = module;
  
  return {
    systemPrompt: content.systemPrompt ? substituteVariables(content.systemPrompt, variableValues) : undefined,
    userPrompt: substituteVariables(content.userPrompt, variableValues),
    assistantPrompt: content.assistantPrompt ? substituteVariables(content.assistantPrompt, variableValues) : undefined
  };
}

// Create module from template with common patterns
export function createModuleFromTemplate(
  name: string,
  template: 'writer' | 'analyzer' | 'coder' | 'qa' | 'custom',
  customPrompt?: string
): PromptxModule {
  const baseModule = createEmptyPromptxModule();
  
  switch (template) {
    case 'writer':
      return {
        ...baseModule,
        metadata: {
          ...baseModule.metadata,
          name: name || 'Writing Assistant',
          description: 'Helps create high-quality written content',
          category: 'core',
          tags: ['writing', 'content', 'assistant']
        },
        content: {
          ...baseModule.content,
          systemPrompt: 'You are an expert writing assistant. Help create clear, engaging, and well-structured content.',
          userPrompt: 'Write {{format}} about {{topic}} in a {{tone}} tone for {{audience}}. {{additional_instructions}}',
          variables: [
            { name: 'topic', type: 'text', description: 'Main topic', required: true },
            { name: 'format', type: 'select', options: ['article', 'blog post', 'essay', 'summary'], defaultValue: 'article' },
            { name: 'tone', type: 'select', options: ['professional', 'casual', 'academic', 'creative'], defaultValue: 'professional' },
            { name: 'audience', type: 'text', description: 'Target audience', placeholder: 'e.g., professionals, students' },
            { name: 'additional_instructions', type: 'text', description: 'Any additional requirements', required: false }
          ]
        }
      };
      
    case 'analyzer':
      return {
        ...baseModule,
        metadata: {
          ...baseModule.metadata,
          name: name || 'Content Analyzer',
          description: 'Analyzes and provides insights on content',
          category: 'specialized',
          tags: ['analysis', 'insights', 'review']
        },
        content: {
          ...baseModule.content,
          systemPrompt: 'You are an expert analyst. Provide thorough, objective analysis with actionable insights.',
          userPrompt: 'Analyze the following {{content_type}}: {{content}}\n\nFocus on: {{analysis_focus}}',
          variables: [
            { name: 'content_type', type: 'select', options: ['text', 'code', 'data', 'document'], defaultValue: 'text' },
            { name: 'content', type: 'text', description: 'Content to analyze', required: true },
            { name: 'analysis_focus', type: 'text', description: 'What to focus on', placeholder: 'e.g., structure, clarity, performance' }
          ]
        }
      };
      
    case 'custom':
      return {
        ...baseModule,
        metadata: {
          ...baseModule.metadata,
          name: name || 'Custom Module',
          description: 'A custom prompt module',
          category: 'custom'
        },
        content: {
          ...baseModule.content,
          userPrompt: customPrompt || 'Enter your custom prompt here...'
        }
      };
      
    default:
      return baseModule;
  }
}

// Export/Import utilities
export const PromptxUtils = {
  validate: validatePromptxModule,
  parse: parsePromptxFile,
  serialize: serializePromptxModule,
  substitute: substituteVariables,
  extract: extractVariableNames,
  generate: generatePrompt,
  createFromTemplate: createModuleFromTemplate,
  createEmpty: createEmptyPromptxModule
};
