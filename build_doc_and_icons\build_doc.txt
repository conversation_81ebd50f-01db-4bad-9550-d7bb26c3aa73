Here is what I'd like you to build:

Catalyst - MVP Build Document v1.3.1
 Project Title: Catalyst - Prompt Engineering Ecosystem (MVP Phase)
 Build Platform: Lovable.dev
 Lead Developer: Metamorphic Labs
 Start Date: 06/26/2025
 Version: MVP 1.3.1

🌟 Overview
Catalyst is the evolved form of PromptBox, a highly advanced prompt engineering ecosystem. It is designed to empower creators, developers, and AI enthusiasts to design, chain, adapt, and optimize AI-driven prompts across multiple models and domains with zero code.
This MVP build is the foundational system required to:
Deploy the Catalyst ecosystem publicly.


Validate user interaction, module structure, and AI orchestration.


Support an initial wave of community and team usage.



🔧 Core MVP Features
✅ 1. Catalyst Core UI (Web App - Lovable.dev)
Minimalist UI styled with Catalyst branding.


Modular drag-and-drop interface for chaining prompt modules.


Prompt input/output window with live AI stream.


Module Library sidebar (pre-built engines + saved modules).


Settings for tone/style, model selection, and memory control.


🔹 2. Prompt Modules (".promptx") System
Support import/export of module files (.promptx).


Module metadata: tags, description, intended model, formatting style.


Live-editing of prompt internals: system, user, input placeholders.


🪡 3. Prompt Engines (Prebuilt Chains)
Writer Engine: Outline > Expand > Style.


Interview Simulator: Role-play Q&A.


Debug Console: Analyze and refine code snippets.


🔗 4. Multi-Model Routing Engine (OpenRouter + LiteLLM)
Native OpenRouter integration with default access to top-tier models.


LiteLLM support for advanced routing, caching, and self-hosting.


Model selection UI with:


Overview


Strengths / Weaknesses


Suggested Roles


Best Use Cases


Token tracking and output formatting optimization for each model.


🎨 5. PromptForge Lite
Advanced builder for custom module crafting.


Syntax highlighting for prompt scripting.


Slot-based variable assignment (e.g. {{topic}}, {{tone}}).


🔄 6. PromptPortals (Model Adapters)
Optimize prompt for target model.


Adaptive filters for output tone, context length, and bias.


🏛️ 7. PromptMemory (Session Only)
MVP includes temp memory per session.


Stores input history, last 10 responses, session context.


⚡ 8. Basic Output Export Tools
Export as: Markdown (.md), JSON (.json), Text (.txt), PromptPack (.promptx).


💻 9. Prompt4Me (Full Intelligent Assistant)
Smart, model-agnostic assistant for generating prompts via guided Q&A.


Asks 3–5 intelligent questions to tailor prompts to user goals, style, tone, and use-case.


Generates fully-structured ".promptx" modules.


Supports all models and prompt types (writing, coding, research, business, etc).


Offers inline editing, export, and workflow integration.


Operates with in-session memory; persistent learning features to arrive in Wave 2.


🔢 10. Chain of Draft System
Multi-stage refinement workflow: Draft > Refine > (optional) Compress > (optional) Contextualize.


Seamless integration with PromptEngines and PromptForge Lite.


Optional stages enabled via checkboxes.


Improves clarity, reduces token load, and prepares content for chaining.


Each stage powered by a modular .promptx file and supports real-time preview.



🚀 Deployment Requirements
Hosted on Lovable.dev with backend serverless support.


User login system (OAuth or email-password).


Data persistence (MongoDB or Firebase) for saved prompts/modules.


Modular architecture for future plugin system (Wave 3).


OpenRouter API integration with Catalyst key.


LiteLLM bridge for routing/self-hosted use cases.



📌 MVP Dev Timeline (Est.)
 Week 1 – UI/UX prototype in Lovable.dev, core input/output setup
 Week 2 – Prompt Modules & chaining system (drag/drop, save/load)
 Week 3 – Multi-model routing with OpenRouter + LiteLLM
 Week 4 – PromptEngines, PromptForge Lite, PromptMemory, Prompt4Me
 Week 5 – Chain of Draft System integration, optimization, and interface testing
 Week 6 – Final QA, bugfixes, deployment config

🎓 Initial Prompt Modules for MVP
Creative Writing Starter Pack


Technical Assistant (Dev QA)


Marketing Funnel Generator


Research Summary Synthesizer



🚪 Key Limitations (MVP Only)
No community sharing system yet


No persistent long-term memory (session only)


No marketplace or promptHarbor integration


No CLI, VSCode, or native desktop integration



🔍 Review Tags
#Catalyst #PromptBoxLegacy #MVP1 #LovableDev #PromptEngineering


-----

🔧 Catalyst Module Build: Core UI (Web App - Lovable.dev)
🎓 Module Purpose
The Catalyst Core UI is the foundation of user interaction across the platform. It brings Catalyst's modular architecture to life through a clean, responsive interface. The Core UI enables drag-and-drop prompt orchestration, live model interaction, module access, and real-time output control with zero code.
Built on Lovable.dev, this interface prioritizes accessibility, flexibility, and extensibility while maintaining the radiant design language of Catalyst.

🔧 Features (MVP)
● Minimalist Catalyst-Branded UI
Elegant, lightweight styling consistent with Catalyst's branding.


Responsive layout optimized for desktop, with future-proofing for mobile/tablet expansion.


Subtle theming (vibrant gradients, soft glow elements, glyph accents).


● Modular Drag-and-Drop Interface
Interactive workspace where users can:


Add, remove, and rearrange prompt modules visually.


Chain modules with visible connection lines.


View module flow order and logic progression.


Reusable templates and layout persistence.


● Prompt Input/Output Panel
Dedicated area to:


View prompt composition in real-time.


Edit raw prompts or structured elements (e.g. system/user inputs).


Stream AI output live as it generates.


Toggle between compact and expanded views for input/output.


● Module Library Sidebar
Sidebar containing:


Saved modules and pre-built engines (e.g. Writer, Interview Bot).


User-generated modules.


Access to Prompt4Me and PromptForge Lite.


Drag modules from sidebar directly into main workspace.


● Settings Panel
Adjustable settings per workspace:


Tone & Style preferences.


Model selection from OpenRouter or LiteLLM list.


Memory toggles (session memory enabled by default).


Output behavior (token limit, stream formatting, retry rules).



🧹 Integration Points
Linked To:
Prompt Modules system (to render .promptx workflows)


PromptForge Lite (via edit handoff from modules)


Prompt4Me (entry point from Module Library or workspace)


Multi-Model Routing Engine (binds selected model to UI context)


Built With:
Lovable.dev's component-driven architecture


Catalyst’s output stream and memory session layer


Drag-and-drop module logic engine (Catalyst Internal)



🧪 Testing Requirements
Chain 3–5 modules and verify drag/drop, reorder, and live linking


Test input/output panel for prompt edit + stream


Validate live module switching and settings adjustment


Ensure stability across default module sets and Prompt4Me flows



🗓️ Development Scope
Estimated Build Time: 3–4 Days


Priority: CRITICAL (Root of UX layer)


Dependencies: Prompt Modules system, Output Engine, OpenRouter/LiteLLM routing


-----

🔹 Catalyst Module Build: Prompt Modules (.promptx) System
🎓 Module Purpose
The Prompt Modules system is the structural backbone of Catalyst. It enables all prompt content, workflows, engines, and user-generated logic to be saved, shared, edited, and executed through a universal format: .promptx.
Modules are interactive, metadata-enriched prompt containers that power everything from basic tasks to advanced AI chains. They ensure compatibility across models, tools, and user skill levels, and are fully integrated into the Catalyst drag-and-drop UI and memory/session stack.

🔧 Features (MVP)
● Import/Export Prompt Modules (.promptx)
Users can import .promptx files from their system or external sources.


Save/export active modules to .promptx, .json, .txt, or .md.


Each file is portable, editable, and sharable.


● Module Metadata Support
 Each module includes structured metadata fields:
Name, Author, Tags


Model Affinity (e.g., GPT-4o, Claude 3.5)


Formatting Style (markdown, plaintext, JSON)


Type (Single Prompt / Chained / Template)


UI View Preferences (Compact, Full, Toggled)


● Live Module Editing
Users can open any module to:


View/edit its system + user prompts


Add/remove placeholders (e.g. {{input}}, {{audience}})


Modify metadata tags or formatting rules


Changes reflected live in the Catalyst workspace and Preview panel.


● Dynamic Prompt Variables
.promptx supports smart slot logic:


Required vs. optional inputs


Predefined lists or enum choices


Default values and auto-fill from session context


● Integration with Prompt4Me + PromptForge
Prompt4Me outputs directly to .promptx


Modules can be sent into PromptForge Lite for advanced logic rewriting



🧩 Integration Points
Linked To:
Catalyst Core UI (workspace rendering + chain linking)


PromptForge Lite (deep edit mode)


PromptEngines (uses chains of .promptx modules)


PromptMemory (remembers module values in-session)


Built With:
Catalyst's internal prompt schema


Metadata validator and variable resolver


Export/formatting adapter for multiple formats



🧪 Testing Requirements
Load and render sample .promptx files


Create new modules from scratch and export them


Edit live metadata, prompt fields, and slots


Test Prompt4Me and PromptForge Lite roundtrips



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: Core UI, PromptForge Lite, Memory (Session)


-----

🪡 Catalyst Module Build: Prompt Engines (Prebuilt Chains)
🎓 Module Purpose
Prompt Engines are curated chains of .promptx modules that automate multi-step prompt workflows. These engines are preconfigured for specific goals, such as writing content, simulating interviews, or debugging code.
They allow users to execute complex tasks without needing to build chains manually, while remaining fully customizable and extensible. Each engine is a modular, editable, step-driven system designed for speed, consistency, and cross-model compatibility.

🔧 Features (MVP)
● Prebuilt Prompt Engines Available in MVP:
Writer Engine: Outline > Expand > Refine


Interview Simulator: Role-based Q&A trainer


Debug Console: Input > Analyze > Suggest Fix


● Engine Architecture
Each engine is a chain of 2–5 .promptx modules


Step order, context retention, and memory sharing defined per engine


Each step editable or swappable in the Catalyst Core UI


● Configurable Engine Parameters
Users can adjust:


Model per step


Input prompt or goal


Output format (e.g. markdown, text block, Q&A pairs)


Tone & voice (globally or per-step)


● Smart Context Linking
Output from one step is fed to the next via Catalyst memory or direct chaining


Inline summarization or transformation between steps is supported


● Full Module Transparency
Each engine’s modules are visible and editable inline


Users can fork, extend, or remix any engine into their own saved variant



🧹 Integration Points
Linked To:
Catalyst Core UI (drag-and-drop editable chains)


Prompt Modules (.promptx)


PromptMemory (step-to-step state retention)


PromptPortals (optimize per model)


Built With:
Catalyst chaining engine (sequential execution framework)


Prompt module runner (handles input/output flow)



🧪 Testing Requirements
Run all default engines and verify full chain execution


Modify individual steps (edit prompt, change model, add logic)


Fork engine and validate chain export/save


Confirm memory/context carryover across steps



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: Prompt Modules, Core UI, PromptMemory


-----

🔗 Catalyst Module Build: Multi-Model Routing Engine (OpenRouter + LiteLLM)
🎓 Module Purpose
The Multi-Model Routing Engine is the intelligent dispatch layer within Catalyst that connects prompt workflows to diverse AI models. It ensures flexible, optimized execution by routing tasks through OpenRouter and LiteLLM — giving users access to top-tier commercial and local models without the complexity of multiple keys or configurations.
This engine supports model switching at both global and per-module levels, enables usage tracking, and dynamically formats prompts for compatibility and performance.

🔧 Features (MVP)
● OpenRouter Integration
Built-in support for OpenRouter model list.


Default routing access to GPT-4o, Claude 3.5, Mistral, and others.


No manual API key configuration required for end-users (uses Catalyst key).


● LiteLLM Integration
Supports advanced routing, caching, throttling, and self-hosted endpoints.


Default LiteLLM config deployed in Catalyst backend.


Allows developers to inject custom routes or override with private/local models.


● Model Selection UI
Dropdown list of available models, each with:


Model Overview


Strengths (e.g. creative reasoning, precision formatting)


Weaknesses (e.g. slower response time, verbose outputs)


Suggested Roles (e.g. writing assistant, data extractor, chatbot logic)


Best Use Cases (e.g. long-form narrative, concise coding, safe Q&A)


Model switcher embedded in:


Global Settings Panel


Per-Module Settings (for advanced chaining workflows)


● Output Optimization + Token Tracking
Automatically adjusts:


Prompt formatting per model (e.g. role tokens, system/user handling)


Token budget and chunking behavior


Output streaming rules (if model supports it)


Display model-specific limits and real-time token usage estimates


● Fallback & Failover Logic (Optional MVP Beta)
If model call fails, suggest retry on alternate model


Users can define preferred fallback order in advanced settings (GPT → Claude → Mistral)



🧩 Integration Points
Linked To:
Core UI (model selection, execution routing)


PromptPortals (adapts prompts per model)


PromptModules + PromptEngines (module-by-module control)


Built With:
OpenRouter API


LiteLLM router/middleware


Catalyst prompt formatting + token estimation logic



🧪 Testing Requirements
Load prompt and route through at least 3 OpenRouter models


Test fallback logic and performance with intentional model failures


Verify token usage display and formatting per model


Test per-module model switching in chained prompts



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: ESSENTIAL


Dependencies: Prompt Modules, Core UI, PromptPortals


-----

🎨 Catalyst Module Build: PromptForge Lite
🎓 Module Purpose
PromptForge Lite is Catalyst’s integrated visual module crafter — a simplified yet powerful system for building, customizing, and remixing .promptx modules without touching raw prompt code.
It empowers users to craft and refine prompts using structured blocks, slot variables, and real-time previews. It is ideal for both casual users and advanced prompt engineers who want full control without complexity.

🔧 Features (MVP)
● Visual Module Editor
Clean UI for editing individual .promptx modules.


Includes sections for:


System prompt


User prompt


Placeholder slots (e.g. {{input}}, {{audience}})


Metadata (tags, model, style, usage type)


● Syntax Highlighting for Prompt Logic
Basic color-coding for system/user sections.


Visual distinction between dynamic variables, static text, and comments.


● Slot-Based Variable Assignment
Easily add or edit placeholders like {{topic}}, {{tone}}, {{length}}.


Default values and variable descriptions supported.


Optional: Define type constraints (dropdowns, toggle fields).


● Live Preview
Users can enter values into slot fields to preview how the full prompt will render.


Preview includes formatting fidelity for the target model (markdown, JSON, etc).


● Edit from Module or Prompt4Me
Users can open any module from the Core UI or Prompt4Me for editing.


Changes sync with Catalyst memory stack and live workspace.


● Save/Export
Save updated module back into user library.


Export to .promptx, .json, .txt, or .md.



🧩 Integration Points
Linked To:
PromptModules (.promptx format compatibility)


Prompt4Me (handoff and refinement layer)


Core UI (Module drag/drop edit access)


PromptEngines (custom module injection into engines)


Built With:
Catalyst’s slot renderer and variable parser


Live template preview engine


Minimal syntax highlighter



🧪 Testing Requirements
Load 3 sample modules and edit each component (system/user/slots/metadata)


Add slot variables and test render output


Run export in all supported formats


Confirm edit-sync with Core UI and Prompt4Me



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: PromptModules, Prompt4Me, Core UI, Export Tools


-----

🔄 Catalyst Module Build: PromptPortals (Model Adapters)
🎓 Module Purpose
PromptPortals serve as intelligent model adapters within Catalyst. Their role is to reshape, fine-tune, or optimize prompts for specific AI models. This ensures that prompts retain their intent, structure, and tone — while being tailored to the quirks, capabilities, and limitations of the selected model.
PromptPortals are especially useful when switching between OpenRouter or LiteLLM models with different formatting styles, token limits, or contextual behavior.

🔧 Features (MVP)
● Adaptive Prompt Transformation
Analyze prompt structure and auto-convert based on model's preferred:


Role formatting (system/user style)


Token constraints (e.g., chunking or shortening logic)


Markdown or plain text style


Instruction density tolerance (verbose vs. minimalist models)


● Tone & Style Normalization Filters
Optional filters adjust prompt tone or structure to suit model behavior:


Make prompts more assertive, cautious, exploratory, etc.


Restructure goals into model-compatible phrasing


● Context-Length Manager
Estimate token load and automatically truncate or summarize long inputs


Show token usage preview post-adaptation


● Bi-Directional Compatibility Layer
Users can:


Adapt prompts to a target model


Reverse-adapt prompts from a model for reuse or remix


● UI Toggle in Prompt Settings
Enable/disable PromptPortal layer per prompt or module


View prompt before and after adaptation side-by-side



🧩 Integration Points
Linked To:
Multi-Model Routing Engine (triggers adaptation based on model)


PromptModules (runs adaptation pre-render or pre-chain)


Prompt4Me (uses behind the scenes when target model selected)


Built With:
Catalyst prompt format schema


Model-specific transformation profiles


Token estimation + summarization engine



🧪 Testing Requirements
Adapt 5+ diverse prompts across GPT-4o, Claude 3.5, Mistral


Confirm formatting, tone, and length adapt per model


Use side-by-side view to validate transformation quality


Test toggle + reverse adaptation logic



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: PromptModules, Multi-Model Routing Engine


-----

🏛️ Catalyst Module Build: PromptMemory (Session Only)
🎓 Module Purpose
PromptMemory is Catalyst’s lightweight memory layer for tracking context and continuity across a single user session. It captures recent prompts, responses, and relevant slot values — enabling smarter chaining, follow-ups, and iterative workflows.
This MVP version is session-only and does not persist across logins. It serves as a contextual backbone for modules like Prompt4Me, PromptEngines, and Chain of Draft, without introducing complex storage or retrieval systems yet.

🔧 Features (MVP)
● Temporary In-Session Memory Store
Stores:


Last 10 user inputs


Last 10 AI outputs


Active prompt/module history (recent .promptx usage)


Named slot variable values (e.g. {{topic}}, {{tone}})


● Memory-Enhanced Chaining Support
Modules like PromptEngines or Chain of Draft can retrieve:


Recent outputs as inputs


Previously filled slots


Active tone/model parameters


● Session Context Panel
View/edit current session memory


Collapse history view into summaries


Clear all / reset options


● System-Wide Context Access Layer
Prompt4Me uses memory to:


Recall answers to earlier questions


Adjust suggestions based on user tone/style


PromptPortals can adapt based on session tone/model preference


● Session Expiry Behavior
All data cleared on logout or session timeout (configurable)


Users prompted to export session or module snapshots before leaving (optional)



🧩 Integration Points
Linked To:
Prompt4Me (Q&A + module crafting memory)


PromptEngines (step-to-step memory usage)


PromptForge Lite (pre-fill variable slots)


PromptModules (slot memory + recent prompt reference)


Built With:
Catalyst memory stack (non-persistent)


Session token & local buffer integration


Memory context layer for .promptx runtime



🧪 Testing Requirements
Run multi-step engines using remembered outputs


Confirm slot autofill behavior from prior modules


Test memory panel for session display/edit/clear


Check behavior on session timeout and logout



🗓️ Development Scope
Estimated Build Time: 2 Days


Priority: HIGH


Dependencies: PromptModules, Core UI, PromptEngines


-----

⚡ Catalyst Module Build: Basic Output Export Tools
🎓 Module Purpose
This module enables users to export their AI-generated outputs, .promptx modules, and session work in standard, readable, and reusable formats. It ensures that work done within Catalyst can be saved, reused outside the platform, or integrated into broader workflows.
The MVP version focuses on basic, high-utility formats that are human-readable, widely compatible, and directly relevant to prompt engineering.

🔧 Features (MVP)
● Supported Export Formats
.md (Markdown): For formatted content, readable in docs, previews, and GitHub


.txt (Plain Text): For clean raw exports


.json (Structured Data): Ideal for model evaluations or system-level reuse


.promptx (Catalyst Module Format): Standard module format, export-ready


● Export Interface
Export available from:


Output panel (for prompt responses)


PromptForge Lite (for module creation)


Prompt4Me (for generated prompt modules)


“Export” button triggers download modal with format picker


● Metadata Inclusion Options
Toggleable checkboxes to include:


Timestamp


Model used


Prompt inputs or slot values


Catalyst module name or ID


● Session Export (Optional MVP Add-on)
Export full session (inputs/outputs) as JSON or Markdown log


Used with PromptMemory for archiving or post-session review



🧩 Integration Points
Linked To:
Prompt4Me (prompt export)


PromptForge Lite (module save/export)


PromptEngines (engine result saving)


PromptMemory (session export support)


Built With:
Catalyst export formatter


File generator + download interface


.promptx schema handler



🧪 Testing Requirements
Export test prompt response in all formats


Export .promptx module and verify metadata


Export full session with memory snapshot (if add-on enabled)


Confirm all downloads are correctly named and structured



🗓️ Development Scope
Estimated Build Time: 1.5 Days


Priority: Medium–High


Dependencies: PromptModules, Prompt4Me, Core UI


-----

💻 Catalyst Module Build: Prompt4Me – Smart Prompt Assistant
🎓 Module Purpose
Prompt4Me is Catalyst’s intelligent prompt generation assistant. It guides users through a short, adaptive conversation to understand their goal, tone, style, and use-case, and outputs a complete .promptx file that can be used directly or edited.
This module is fully integrated into the Catalyst UI and serves as a frictionless starting point for any user, whether novice or pro.

🔧 Features (MVP)
● Model-Agnostic Prompt Assistant
Compatible with all Catalyst-supported models (via OpenRouter + LiteLLM).


Prompt4Me is not limited to specific models—only guided by user input and selected route.


● Guided Q&A Workflow
Starts with a conversation UI embedded in Catalyst’s main workspace.


Asks 3–5 intelligent, branching questions:


Use-case (e.g., writing, coding, analysis)


Target style and tone


Output goals


Model preference (optional)


Platform-specific tweaks (if known)


● Output
Generates a fully structured .promptx file.


Includes:


System prompt


User prompt scaffolding


Variable placeholders (e.g., {{goal}}, {{style}})


Metadata (tags, target model, formatting style)


Can be:


Edited inline


Saved to Module Library


Exported via .promptx, .json, .md, or .txt


● In-Session Context
Stores session answers and prompt generation logic for real-time feedback.


Remembers Q&A flow within the session for easy backtracking and adjustment.


● UI/UX Design
Sidebar module (like PromptForge Lite) labeled:
 🔍 Prompt4Me – Smart Prompt Assistant


Expands into a focused assistant mode on click.


Displays generated prompt as editable live preview.


Users can:


Copy/paste immediately


Edit live inline


Export only if desired



🧹 Integration Points
Linked to:
Prompt Modules system (writes .promptx)


Prompt Engines (can export to chains or modules)


PromptForge Lite (users can send generated prompt to Forge for advanced tweaking)


Built With:
Catalyst's Core Input/Output architecture


Catalyst Memory system (session-only in MVP)


OpenRouter + LiteLLM routing backend



🧪 Testing Requirements
Generate prompt across at least 5 common use-cases


Verify model-agnostic output and formatting


Test integration into saved Modules and PromptForge workflows


Validate session memory preserves prompt logic during active edits



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: None (fully independent module)


-----

🪡 Catalyst Module Build: Chain of Draft System
🎓 Module Purpose
The Chain of Draft system is a powerful multi-stage prompt refinement framework built into Catalyst. It enables outputs to be drafted, refined, compressed, and contextualized in distinct, modular steps — improving quality, reducing token load, and allowing granular control over AI workflows.
Chain of Draft was integrated into Catalyst to support more dynamic, composable AI chains. It is especially useful in writing, summarization, and style-sensitive tasks where users or systems benefit from staging the creative or technical process.
This module enables users to activate a multi-pass drafting sequence for any compatible prompt flow.

🔧 Features (MVP)
● Draft Staging Engine
Supports sequential execution of up to 4 stages:


Initial Draft Pass – generates raw content


Refinement Pass – improves clarity, tone, formatting, or depth


Compression Pass (optional checkbox) – condenses verbose output while retaining meaning


Contextual Postpass (optional checkbox) – tailors content for follow-up chaining or reuse


● Integrated with PromptEngines and PromptForge Lite
Users can design or apply Chain of Draft workflows from within:


🪡 Prebuilt Engines (Writer, Debug, Interview)


🎨 PromptForge Lite (custom chaining and logic design)


● Configurable Per Chain
Each stage is powered by a dedicated .promptx module


Users can:


Swap out stage modules


Adjust tone or model per stage


Toggle optional stages via checkboxes in the engine config


● Token Optimization Mode
Built-in logic analyzes whether compression stage is beneficial based on:


Token thresholds


Output length-to-quality ratios


Suggests enabling Compression Pass when budget is constrained


● Real-Time Preview of Each Stage (MVP Lite)
Users can expand any step to preview intermediate output in real time


Helps with trust, learning, and manual overrides



🧩 Integration Points
Linked To:
PromptEngines (stage-driven workflows)


PromptModules (.promptx-based chains)


PromptForge Lite (design and logic editor)


PromptMemory (to track context across steps)


PromptPortals (for optimizing each stage per model)


Built With:
Catalyst chaining engine


Output router and stream manager


Prompt formatting and token budget advisor



🧪 Testing Requirements
Execute all 3 default Chain of Draft flows (Writer, Interview, Debug)


Enable/disable optional stages and confirm logic change


Verify token usage with and without compression pass


Check memory/context carryover in postpass mode



🗓️ Development Scope
Estimated Build Time: 2–3 Days


Priority: HIGH


Dependencies: PromptEngines, PromptModules, Core UI, Memory System



✅ Benefits Recap
Benefit
Impact
🔄 Iterative Refinement
Improves clarity, tone, and quality in stages instead of one-shot prompts.
💬 Token Efficiency
Optional compression stage saves cost and space for long outputs.
🎓 Enhanced Modularity
Prompts are cleaner, reusable, and swappable at each draft stage.
🔗 Chain-Aware Design
Makes chaining smarter by contextualizing final output for next use.
👤 User Control
Writers, coders, researchers can shape tone and strategy step-by-step.
📦 Built on .promptx
Each stage is just a swappable module, so users can remix or upgrade easily.