"use client"

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Settings, 
  Zap, 
  Brain, 
  Shield, 
  Palette,
  Key,
  Globe,
  Save,
  RotateCcw
} from "lucide-react";

interface ModelConfig {
  id: string;
  name: string;
  provider: string;
  strengths: string[];
  weaknesses: string[];
  maxTokens: number;
  costPer1k: number;
  speed: "Fast" | "Medium" | "Slow";
  quality: "High" | "Medium" | "Low";
}

const availableModels: ModelConfig[] = [
  {
    id: "gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    strengths: ["Reasoning", "Code", "Analysis"],
    weaknesses: ["Cost", "Speed"],
    maxTokens: 128000,
    costPer1k: 0.03,
    speed: "Medium",
    quality: "High"
  },
  {
    id: "claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet", 
    provider: "Anthropic",
    strengths: ["Writing", "Analysis", "Safety"],
    weaknesses: ["Code", "Math"],
    maxTokens: 200000,
    costPer1k: 0.015,
    speed: "Fast",
    quality: "High"
  },
  {
    id: "gemini-pro",
    name: "Gemini Pro",
    provider: "Google",
    strengths: ["Multimodal", "Speed", "Cost"],
    weaknesses: ["Reasoning", "Consistency"],
    maxTokens: 32000,
    costPer1k: 0.001,
    speed: "Fast",
    quality: "Medium"
  }
];

export function SettingsPanel() {
  const [selectedModel, setSelectedModel] = useState("gpt-4o");
  const [temperature, setTemperature] = useState([0.7]);
  const [maxTokens, setMaxTokens] = useState([2048]);
  const [topP, setTopP] = useState([0.9]);
  const [streamingEnabled, setStreamingEnabled] = useState(true);
  const [autoSave, setAutoSave] = useState(true);

  const selectedModelConfig = availableModels.find(m => m.id === selectedModel);

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold gradient-text">Settings & Configuration</h2>
            <p className="text-sm text-muted-foreground">Customize your Catalyst experience</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2">
              <RotateCcw className="w-4 h-4" />
              Reset
            </Button>
            <Button size="sm" className="gap-2">
              <Save className="w-4 h-4" />
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-6">
          <Tabs defaultValue="models" className="space-y-6">
            <TabsList className="bg-muted/20">
              <TabsTrigger value="models" className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI Models
              </TabsTrigger>
              <TabsTrigger value="parameters" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Parameters
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Key className="w-4 h-4" />
                API Keys
              </TabsTrigger>
              <TabsTrigger value="interface" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Interface
              </TabsTrigger>
            </TabsList>

            <TabsContent value="models" className="space-y-6">
              <Card className="catalyst-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5 text-catalyst-purple" />
                    Model Selection
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {availableModels.map((model) => (
                    <Card 
                      key={model.id} 
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedModel === model.id 
                          ? 'ring-2 ring-catalyst-purple catalyst-glow' 
                          : 'hover:catalyst-glow'
                      }`}
                      onClick={() => setSelectedModel(model.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-semibold">{model.name}</h4>
                            <p className="text-sm text-muted-foreground">{model.provider}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {model.speed}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {model.quality}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-muted-foreground mb-1">Strengths</p>
                            <div className="flex flex-wrap gap-1">
                              {model.strengths.map((strength) => (
                                <Badge key={strength} variant="secondary" className="text-xs">
                                  {strength}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground mb-1">Considerations</p>
                            <div className="flex flex-wrap gap-1">
                              {model.weaknesses.map((weakness) => (
                                <Badge key={weakness} variant="outline" className="text-xs">
                                  {weakness}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>Max Tokens: {model.maxTokens.toLocaleString()}</span>
                          <span>Cost: ${model.costPer1k}/1K tokens</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="parameters" className="space-y-6">
              <Card className="catalyst-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5 text-catalyst-purple" />
                    Generation Parameters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="temperature">Temperature</Label>
                      <span className="text-sm text-muted-foreground">{temperature[0]}</span>
                    </div>
                    <Slider
                      id="temperature"
                      min={0}
                      max={2}
                      step={0.1}
                      value={temperature}
                      onValueChange={setTemperature}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Controls randomness. Lower values = more focused, higher values = more creative.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="max-tokens">Max Tokens</Label>
                      <span className="text-sm text-muted-foreground">{maxTokens[0]}</span>
                    </div>
                    <Slider
                      id="max-tokens"
                      min={1}
                      max={selectedModelConfig?.maxTokens || 4096}
                      step={1}
                      value={maxTokens}
                      onValueChange={setMaxTokens}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Maximum number of tokens to generate in the response.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="top-p">Top P</Label>
                      <span className="text-sm text-muted-foreground">{topP[0]}</span>
                    </div>
                    <Slider
                      id="top-p"
                      min={0}
                      max={1}
                      step={0.01}
                      value={topP}
                      onValueChange={setTopP}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Controls diversity via nucleus sampling. Lower values = more focused.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="streaming">Enable Streaming</Label>
                        <p className="text-xs text-muted-foreground">Show responses as they generate</p>
                      </div>
                      <Switch
                        id="streaming"
                        checked={streamingEnabled}
                        onCheckedChange={setStreamingEnabled}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-save">Auto-save Sessions</Label>
                        <p className="text-xs text-muted-foreground">Automatically save conversation history</p>
                      </div>
                      <Switch
                        id="auto-save"
                        checked={autoSave}
                        onCheckedChange={setAutoSave}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api" className="space-y-6">
              <Card className="catalyst-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5 text-catalyst-purple" />
                    API Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="openai-key">OpenAI API Key</Label>
                    <Input
                      id="openai-key"
                      type="password"
                      placeholder="sk-..."
                      className="catalyst-border"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="anthropic-key">Anthropic API Key</Label>
                    <Input
                      id="anthropic-key"
                      type="password"
                      placeholder="sk-ant-..."
                      className="catalyst-border"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="openrouter-key">OpenRouter API Key</Label>
                    <Input
                      id="openrouter-key"
                      type="password"
                      placeholder="sk-or-..."
                      className="catalyst-border"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="interface" className="space-y-6">
              <Card className="catalyst-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="w-5 h-5 text-catalyst-purple" />
                    Interface Preferences
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Dark Mode</Label>
                      <p className="text-xs text-muted-foreground">Use dark theme</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Compact Mode</Label>
                      <p className="text-xs text-muted-foreground">Reduce spacing and padding</p>
                    </div>
                    <Switch />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Show Tooltips</Label>
                      <p className="text-xs text-muted-foreground">Display helpful tooltips</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </ScrollArea>
    </div>
  );
}
