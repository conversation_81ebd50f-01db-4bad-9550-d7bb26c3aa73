"use client"

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Zap, 
  RefreshCw, 
  Download, 
  Copy, 
  History,
  Play,
  Square,
  MoreHorizontal
} from "lucide-react";

interface SessionHistoryItem {
  id: string;
  title: string;
  timestamp: string;
  model: string;
  tokens: number;
  preview: string;
}

const sessionHistory: SessionHistoryItem[] = [
  {
    id: "1",
    title: "Blog Post: AI in Healthcare",
    timestamp: "2 minutes ago",
    model: "GPT-4o",
    tokens: 1247,
    preview: "Artificial Intelligence is revolutionizing healthcare by enabling more accurate diagnoses..."
  },
  {
    id: "2", 
    title: "Code Review: React Component",
    timestamp: "15 minutes ago",
    model: "Claude 3.5",
    tokens: 892,
    preview: "The component structure looks good overall. Here are some suggestions for improvement..."
  },
  {
    id: "3",
    title: "Interview Questions: Senior Dev",
    timestamp: "1 hour ago", 
    model: "GPT-4o",
    tokens: 654,
    preview: "Here are comprehensive interview questions for a senior developer position..."
  }
];

export function OutputPanel() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingText, setStreamingText] = useState("");
  const [activeOutputTab, setActiveOutputTab] = useState("live");

  const mockStreamingText = `# AI-Powered Healthcare Revolution

Artificial Intelligence is fundamentally transforming the healthcare landscape, offering unprecedented opportunities to improve patient outcomes, reduce costs, and enhance the overall quality of care. This technological revolution is not just a distant future concept—it's happening now, with real-world applications already making significant impacts across various medical specialties.

## Key Areas of Impact

### 1. Diagnostic Accuracy
AI systems are now capable of analyzing medical images with accuracy that often surpasses human specialists. From detecting early-stage cancers in radiology scans to identifying diabetic retinopathy in eye examinations, machine learning algorithms are becoming invaluable diagnostic tools.

### 2. Personalized Treatment Plans
By analyzing vast amounts of patient data, including genetic information, medical history, and lifestyle factors, AI can help physicians develop highly personalized treatment strategies that are more effective and have fewer side effects.

### 3. Drug Discovery and Development
The traditional drug discovery process, which typically takes 10-15 years and costs billions of dollars, is being accelerated through AI-powered molecular analysis and predictive modeling...`;

  useEffect(() => {
    if (isStreaming) {
      const words = mockStreamingText.split(' ');
      let currentIndex = 0;
      
      const interval = setInterval(() => {
        if (currentIndex < words.length) {
          setStreamingText(prev => prev + (currentIndex === 0 ? '' : ' ') + words[currentIndex]);
          currentIndex++;
        } else {
          setIsStreaming(false);
          clearInterval(interval);
        }
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  const startStreaming = () => {
    setStreamingText("");
    setIsStreaming(true);
  };

  const stopStreaming = () => {
    setIsStreaming(false);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold gradient-text">AI Output Stream</h2>
            <p className="text-sm text-muted-foreground">Live results from your prompt chains</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={isStreaming ? stopStreaming : startStreaming}
              className="gap-2"
            >
              {isStreaming ? (
                <>
                  <Square className="w-4 h-4" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  Run Chain
                </>
              )}
            </Button>
            
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <Tabs value={activeOutputTab} onValueChange={setActiveOutputTab} className="h-full flex flex-col">
          <div className="px-6 py-2 border-b border-border">
            <TabsList className="bg-muted/20">
              <TabsTrigger value="live" className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Live Output
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <History className="w-4 h-4" />
                Session History
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            <TabsContent value="live" className="h-full m-0">
              <div className="h-full p-6">
                <Card className="h-full prompt-panel">
                  <div className="p-6 h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <Badge variant="secondary" className="gap-2">
                        <div className={`w-2 h-2 rounded-full ${isStreaming ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`}></div>
                        {isStreaming ? 'Streaming Active' : 'Ready'}
                      </Badge>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" className="gap-2">
                          <Copy className="w-4 h-4" />
                          Copy
                        </Button>
                        <Button variant="ghost" size="sm" className="gap-2">
                          <RefreshCw className="w-4 h-4" />
                          Regenerate
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <ScrollArea className="flex-1">
                      <div className="prose prose-invert max-w-none">
                        <div className="whitespace-pre-wrap text-sm leading-relaxed">
                          {streamingText || "Click 'Run Chain' to start generating content..."}
                          {isStreaming && <span className="animate-pulse">|</span>}
                        </div>
                      </div>
                    </ScrollArea>

                    {streamingText && (
                      <div className="mt-4 pt-4 border-t border-border flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-4">
                          <span>Model: GPT-4o</span>
                          <span>Tokens: {streamingText.split(' ').length * 1.3 | 0}</span>
                          <span>Time: {isStreaming ? 'Generating...' : '2.3s'}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          Quality: High
                        </Badge>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="history" className="h-full m-0">
              <div className="h-full p-6">
                <div className="space-y-3">
                  {sessionHistory.map((item) => (
                    <Card key={item.id} className="module-card cursor-pointer">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{item.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {item.model}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                          <span>{item.timestamp}</span>
                          <span>{item.tokens} tokens</span>
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {item.preview}
                        </p>
                        <div className="flex items-center gap-2 mt-3">
                          <Button variant="ghost" size="sm" className="text-xs h-7">
                            View Full
                          </Button>
                          <Button variant="ghost" size="sm" className="text-xs h-7">
                            Export
                          </Button>
                          <Button variant="ghost" size="sm" className="text-xs h-7">
                            Reuse
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
