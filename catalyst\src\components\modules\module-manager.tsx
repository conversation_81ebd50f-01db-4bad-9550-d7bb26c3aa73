"use client"

import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Upload,
  Download,
  Plus,
  FileText,
  Search,
  Edit,
  Trash2,
  Copy,
  Play
} from 'lucide-react';
import { PromptxModule, PromptxValidationError } from '@/types/promptx';
import { PromptxUtils } from '@/lib/promptx';

interface ModuleManagerProps {
  modules: PromptxModule[];
  onModuleLoad: (module: PromptxModule) => void;
  onModuleCreate: (template: string) => void;
  onModuleEdit: (module: PromptxModule) => void;
  onModuleDelete: (moduleId: string) => void;
  onModuleExecute: (module: PromptxModule) => void;
}

export function ModuleManager({
  modules,
  onModuleLoad,
  onModuleCreate,
  onModuleEdit,
  onModuleDelete,
  onModuleExecute
}: ModuleManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [importErrors, setImportErrors] = useState<PromptxValidationError[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Filter modules based on search and category
  const filteredModules = modules.filter(module => {
    const matchesSearch = module.metadata.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         module.metadata.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         module.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || module.metadata.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      const { module, errors } = PromptxUtils.parse(content);
      
      if (module) {
        onModuleLoad(module);
        setImportErrors([]);
      } else {
        setImportErrors(errors);
      }
    };
    reader.readAsText(file);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle module export
  const handleModuleExport = (module: PromptxModule) => {
    const content = PromptxUtils.serialize(module);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${module.metadata.name.replace(/[^a-zA-Z0-9]/g, '_')}.promptx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get category badge color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'core': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'engine': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'specialized': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'custom': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'template': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'single': return '📄';
      case 'chained': return '🔗';
      case 'template': return '📋';
      default: return '📄';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-3 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search modules..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 bg-card border border-border rounded-md text-sm"
          >
            <option value="all">All Categories</option>
            <option value="core">Core</option>
            <option value="engine">Engine</option>
            <option value="specialized">Specialized</option>
            <option value="custom">Custom</option>
            <option value="template">Template</option>
          </select>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            className="catalyst-border"
          >
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          
          <Button
            size="sm"
            onClick={() => onModuleCreate('custom')}
            className="catalyst-gradient-bg"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Module
          </Button>
        </div>
      </div>

      {/* Import Errors */}
      {importErrors.length > 0 && (
        <Card className="border-red-500/50 bg-red-500/10">
          <CardHeader>
            <CardTitle className="text-red-400 text-sm">Import Errors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              {importErrors.map((error, index) => (
                <div key={index} className="text-sm text-red-300">
                  <span className="font-mono text-xs">{error.path}:</span> {error.message}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Module Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredModules.map((module) => (
          <Card key={`${module.metadata.name}-${module.metadata.createdAt}`} className="module-card group">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getTypeIcon(module.metadata.type)}</span>
                  <div>
                    <CardTitle className="text-sm font-medium line-clamp-1">
                      {module.metadata.name}
                    </CardTitle>
                    <CardDescription className="text-xs line-clamp-2 mt-1">
                      {module.metadata.description}
                    </CardDescription>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-1 mt-2">
                <Badge className={getCategoryColor(module.metadata.category)}>
                  {module.metadata.category}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  v{module.metadata.version}
                </Badge>
                {module.metadata.modelAffinity && module.metadata.modelAffinity.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {module.metadata.modelAffinity[0]}
                    {module.metadata.modelAffinity.length > 1 && ` +${module.metadata.modelAffinity.length - 1}`}
                  </Badge>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-1 mb-3">
                {module.metadata.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {module.metadata.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{module.metadata.tags.length - 3}
                  </Badge>
                )}
              </div>
              
              <div className="text-xs text-muted-foreground mb-3">
                {module.content.variables.length} variable{module.content.variables.length !== 1 ? 's' : ''}
                {module.metadata.author && ` • by ${module.metadata.author}`}
              </div>
              
              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onModuleExecute(module)}
                  className="h-8 px-2"
                >
                  <Play className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onModuleEdit(module)}
                  className="h-8 px-2"
                >
                  <Edit className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleModuleExport(module)}
                  className="h-8 px-2"
                >
                  <Download className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => navigator.clipboard.writeText(PromptxUtils.serialize(module))}
                  className="h-8 px-2"
                >
                  <Copy className="w-3 h-3" />
                </Button>
                {module.metadata.category === 'custom' && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onModuleDelete(`${module.metadata.name}-${module.metadata.createdAt}`)}
                    className="h-8 px-2 text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredModules.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <CardTitle className="text-lg mb-2">No modules found</CardTitle>
            <CardDescription className="mb-4">
              {searchTerm || selectedCategory !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first module or import existing ones'
              }
            </CardDescription>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => onModuleCreate('custom')}>
                <Plus className="w-4 h-4 mr-2" />
                Create Module
              </Button>
              <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                <Upload className="w-4 h-4 mr-2" />
                Import Module
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".promptx,.json"
        onChange={handleFileImport}
        className="hidden"
      />
    </div>
  );
}
